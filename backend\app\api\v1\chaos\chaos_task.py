"""
混沌测试任务API路由
"""
from typing import List
from fastapi import APIRouter, Depends, Query

from app.api.deps import get_current_user, get_current_superuser
from app.core.dependencies import get_chaos_task_service
from app.core.responses import response_builder
from app.models.user.user import User
from app.services.chaos.chaos_task_service import ChaosTaskService
from app.schemas.base import PaginationResponse
from app.schemas.chaos.chaos_task import (
    ChaosTaskCreate, ChaosTaskUpdate, ChaosTaskResponse, ChaosTaskExecuteRequest,
    ChaosTaskBatchRequest, ChaosTaskQuery, ChaosTaskPageResponse
)

router = APIRouter()


@router.get("", response_model=ChaosTaskPageResponse, summary="查询混沌测试任务列表")
async def list_chaos_tasks(
    service: ChaosTaskService = Depends(get_chaos_task_service),
    query: ChaosTaskQuery = Depends(),  # 自动接收并校验查询参数
    _current_user: User = Depends(get_current_user)
):
    """查询混沌测试任务列表，支持关键词搜索、状态筛选和分页"""
    result = await service.list_chaos_tasks(query)
    return response_builder.success(result)


@router.post("", response_model=ChaosTaskResponse, status_code=201, summary="创建混沌测试任务")
async def create_chaos_task(
    task_data: ChaosTaskCreate,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """创建新的混沌测试任务，需要用户权限"""
    task = await service.create(task_data, str(current_user.id))
    return response_builder.created(task)


@router.get("/{task_id}", response_model=ChaosTaskResponse, summary="获取混沌测试任务详情")
async def get_chaos_task(
    task_id: int,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    _current_user: User = Depends(get_current_user)
):
    """获取指定ID的混沌测试任务详情"""
    task = await service.get_by_id(task_id)
    return response_builder.success(task)


@router.put("/{task_id}", response_model=ChaosTaskResponse, summary="更新混沌测试任务")
async def update_chaos_task(
    task_id: int,
    task_data: ChaosTaskUpdate,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """更新混沌测试任务信息，支持修改基本信息和配置"""
    task = await service.update(task_id, task_data, str(current_user.id))
    return response_builder.success(task)


@router.delete("/{task_id}", status_code=204, summary="删除混沌测试任务")
async def delete_chaos_task(
    task_id: int,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """删除混沌测试任务"""
    await service.delete(task_id)
    # HTTP 204 No Content - 删除成功，无响应体


@router.post("/{task_id}/execute", response_model=dict, summary="执行混沌测试任务")
async def execute_chaos_task(
    task_id: int,
    request: ChaosTaskExecuteRequest,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """执行混沌测试任务"""
    result = await service.execute_task(task_id, request, current_user.id)
    return response_builder.success(result)


@router.post("/{task_id}/pause", status_code=204, summary="暂停混沌测试任务")
async def pause_chaos_task(
    task_id: int,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """暂停混沌测试任务执行"""
    await service.pause_task(task_id, current_user.id)
    # HTTP 204 No Content - 暂停成功，无响应体


@router.post("/{task_id}/terminate", status_code=204, summary="终止混沌测试任务")
async def terminate_chaos_task(
    task_id: int,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """终止混沌测试任务执行"""
    await service.terminate_task(task_id, current_user.id)
    # HTTP 204 No Content - 终止成功，无响应体


@router.post("/{task_id}/reset", response_model=bool, summary="重置混沌测试任务")
async def reset_chaos_task(
    task_id: int,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):

    result = await service.reset_task(task_id, current_user.id)
    return response_builder.success(result)


@router.post("/batch", response_model=dict, summary="批量操作混沌测试任务")
async def batch_operation_chaos_tasks(
    request: ChaosTaskBatchRequest,
    service: ChaosTaskService = Depends(get_chaos_task_service),
    current_user: User = Depends(get_current_user)
):
    """批量操作混沌测试任务（执行、暂停、终止、删除）"""
    results = {
        "success_count": 0,
        "failed_count": 0,
        "errors": []
    }

    for task_id in request.task_ids:
        try:
            if request.action == "execute":
                execute_request = ChaosTaskExecuteRequest()
                await service.execute_task(task_id, execute_request, current_user.id)
            elif request.action == "pause":
                await service.pause_task(task_id, current_user.id)
            elif request.action == "terminate":
                await service.terminate_task(task_id, current_user.id)
            elif request.action == "delete":
                await service.delete(task_id)

            results["success_count"] += 1
        except Exception as e:
            results["failed_count"] += 1
            results["errors"].append(f"任务 {task_id}: {str(e)}")

    return response_builder.success(results)

