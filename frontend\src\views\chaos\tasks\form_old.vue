<template>
  <div class="task-form">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>{{ pageTitle }}</h2>
        <p class="header-desc">{{ pageDescription }}</p>
      </div>
      <div class="header-right">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ submitButtonText }}
        </el-button>
      </div>
    </div>

    <!-- 分步表单 -->
    <div class="form-container">
      <el-steps :active="currentStep" align-center>
        <el-step title="基本信息" description="设置任务基本信息" />
        <el-step title="故障配置" description="配置故障注入参数" />
        <el-step title="执行计划" description="设置执行方式和时间" />
        <el-step title="确认提交" description="确认配置并提交任务" />
      </el-steps>

      <div class="step-content">
        <!-- 第一步：基本信息 -->
        <div v-show="currentStep === 0" class="step-panel">
          <el-form
            ref="basicFormRef"
            :model="formData"
            :rules="basicRules"
            label-width="120px"
            class="step-form"
          >
            <el-form-item label="任务名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入任务名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="任务描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入任务描述（可选）"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="目标环境" prop="env_ids">
              <el-select
                v-model="formData.env_ids"
                multiple
                placeholder="请选择目标环境"
                style="width: 100%"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
              >
                <el-option
                  v-for="env in environments"
                  :key="env.id"
                  :label="env.name"
                  :value="env.id"
                />
              </el-select>
              <div class="form-tip">
                已选择 {{ formData.env_ids.length }} 个环境
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 第二步：故障配置 -->
        <div v-show="currentStep === 1" class="step-panel-fault-config">
          <el-form
            ref="faultFormRef"
            :model="formData"
            :rules="faultRules"
            label-width="120px"
            class="step-form"
          >
            <el-form-item label="故障分类" prop="fault_category">
              <el-select
                v-model="selectedCategory"
                placeholder="请选择故障分类"
                @change="handleCategoryChange"
                style="width: 20%"
              >
                <el-option
                  v-for="category in faultCategories"
                  :key="category.key"
                  :label="category.label"
                  :value="category.key"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              v-if="selectedCategory"
              label="故障类型"
              prop="fault_type"
            >
              <el-radio-group v-model="formData.fault_type" @change="handleFaultTypeChange">
                <el-radio-button
                  v-for="faultType in currentCategoryFaultTypes"
                  :key="faultType.key"
                  :value="faultType.key"
                >
                  {{ faultType.label }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>

            <!-- 故障配置 - 左右分栏布局 -->
            <div v-if="formData.fault_type" class="fault-config-container">
              <!-- 左侧：参数配置区域 -->
              <div class="fault-params-section">
                <div class="params-header">
                  <h4>故障参数配置</h4>
                  <div class="params-actions">
                    <el-button size="small" @click="resetParams">清空参数</el-button>
                    <el-button size="small" @click="formatJson">格式化JSON</el-button>
                  </div>
                </div>

              <!-- JSON编辑器 -->
              <div class="json-editor-container">
                <el-input
                  v-model="jsonParams"
                  type="textarea"
                  :rows="12"
                  placeholder="请输入JSON格式的参数配置"
                  @input="handleJsonChange"
                  class="json-editor"
                />

                <!-- JSON提示和示例 -->
                <div class="json-info">
                  <div class="json-status">
                    <el-tag v-if="jsonValid" type="success" size="small">
                      <el-icon><Check /></el-icon>
                      JSON格式正确
                    </el-tag>
                    <el-tag v-else type="danger" size="small">
                      <el-icon><Close /></el-icon>
                      JSON格式错误
                    </el-tag>
                    <span class="param-count">参数数量: {{ Object.keys(formData.fault_params).length }}</span>
                  </div>

                  <el-collapse class="json-help">
                    <el-collapse-item title="查看参数示例" name="example">
                      <div class="json-examples">
                        <!-- 未选择模板时的提示 -->
                        <div v-if="!selectedScenario" class="no-scenario-tip">
                          <el-alert
                            title="参数示例提示"
                            type="info"
                            :closable="false"
                            show-icon
                          >
                            <template #default>
                              <p>请先选择一个内置模板，然后查看该模板的参数示例</p>
                              <p>选择模板后，系统将自动加载该模板的完整参数结构</p>
                            </template>
                          </el-alert>
                        </div>

                        <!-- 选择模板后显示参数示例 -->
                        <div v-else class="scenario-examples">
                          <h5>当前模板参数示例：</h5>
                          <div class="current-scenario-info">
                            <div class="scenario-meta">
                              <el-tag type="success" size="small">{{ selectedScenario.name }}</el-tag>
                              <el-tag :type="getFaultTypeTagType(selectedScenario.fault_type)" size="small">
                                {{ getFaultTypeLabel(selectedScenario.fault_type) }}
                              </el-tag>
                            </div>
                            <p class="scenario-desc">{{ selectedScenario.description }}</p>
                          </div>

                          <div class="example-content">
                            <h6>参数结构：</h6>
                            <pre class="scenario-params">{{ formatScenarioParams(selectedScenario) }}</pre>

                            <div v-if="selectedScenario.param_schema" class="param-schema">
                              <h6>参数说明：</h6>
                              <div class="schema-info">
                                <div class="schema-header">
                                  <span class="header-name">参数名</span>
                                  <span class="header-required">必选</span>
                                  <span class="header-type">类型</span>
                                  <span class="header-desc">说明</span>
                                </div>
                                <div
                                  v-for="(schema, key) in selectedScenario.param_schema"
                                  :key="key"
                                  class="schema-item"
                                  :class="{ 'required-param': isRequiredParam(key, selectedScenario) }"
                                >
                                  <span class="param-name">{{ key }}</span>
                                  <span class="param-required">
                                    <el-tag
                                      v-if="isRequiredParam(key, selectedScenario)"
                                      type="danger"
                                      size="small"
                                    >
                                      必选
                                    </el-tag>
                                    <el-tag
                                      v-else
                                      type="info"
                                      size="small"
                                    >
                                      可选
                                    </el-tag>
                                  </span>
                                  <span class="param-type">{{ schema.type || 'string' }}</span>
                                  <span class="param-desc">{{ schema.description || '无描述' }}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
              </div>

              <!-- 右侧：模板选择区域 -->
              <div class="scenario-templates-section">
                <div class="templates-header">
                  <h4>预设模板</h4>
                  <div v-if="selectedScenario" class="selected-template-info">
                    <el-tag type="success" size="small">{{ selectedScenario.name }}</el-tag>
                    <el-button size="small" @click="clearSelectedScenario">清除</el-button>
                  </div>
                </div>

                <div class="templates-content">
                  <!-- 场景模板选择 -->


              <!-- 内置模板 -->
              <div v-if="groupedScenarios.hasBuiltin" class="template-section">
                <h5>内置模板</h5>
                <div class="template-grid">
                  <div
                    v-for="scenario in groupedScenarios.builtin"
                    :key="scenario.id"
                    class="template-card builtin"
                    :class="{ active: selectedScenario?.id === scenario.id }"
                    @click="handleSelectScenario(scenario)"
                  >
                    <div class="template-header">
                      <span class="template-name">{{ scenario.name }}</span>
                      <div class="template-tags">
                        <el-tag size="small" type="success">内置</el-tag>
                        <el-tag size="small" :type="getFaultTypeTagType(scenario.fault_type)">
                          {{ getFaultTypeLabel(scenario.fault_type) }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="template-desc">{{ scenario.description }}</div>
                    <div class="template-usage">使用次数: {{ scenario.usage_count || 0 }}</div>
                  </div>
                </div>
              </div>

              <!-- 自定义模板 -->
              <div v-if="groupedScenarios.hasCustom" class="template-section">
                <h5>自定义模板</h5>
                <div class="template-grid">
                  <div
                    v-for="scenario in groupedScenarios.custom"
                    :key="scenario.id"
                    class="template-card custom"
                    :class="{ active: selectedScenario?.id === scenario.id }"
                    @click="handleSelectScenario(scenario)"
                  >
                    <div class="template-header">
                      <span class="template-name">{{ scenario.name }}</span>
                      <div class="template-tags">
                        <el-tag size="small" type="info">自定义</el-tag>
                        <el-tag size="small" :type="getFaultTypeTagType(scenario.fault_type)">
                          {{ getFaultTypeLabel(scenario.fault_type) }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="template-desc">{{ scenario.description }}</div>
                    <div class="template-usage">使用次数: {{ scenario.usage_count || 0 }}</div>
                  </div>
                </div>
              </div>

                  <!-- 无模板提示 -->
                  <div v-if="!groupedScenarios.hasBuiltin && !groupedScenarios.hasCustom" class="no-templates">
                    <el-empty description="暂无可用的场景模板" />
                  </div>

                  <!-- 当前选择的模板详情 -->
                  <div v-if="selectedScenario" class="selected-scenario-detail">
                    <div class="detail-header">
                      <h5>模板详情</h5>
                    </div>
                    <div class="detail-content">
                      <div class="scenario-info">
                        <div class="scenario-name">{{ selectedScenario.name }}</div>
                        <div class="scenario-type">
                          <el-tag :type="getFaultTypeTagType(selectedScenario.fault_type)" size="small">
                            {{ getFaultTypeLabel(selectedScenario.fault_type) }}
                          </el-tag>
                          <el-tag v-if="selectedScenario.is_builtin" type="success" size="small">内置</el-tag>
                          <el-tag v-else type="info" size="small">自定义</el-tag>
                        </div>
                        <div class="scenario-desc">{{ selectedScenario.description }}</div>
                      </div>

                      <div v-if="selectedScenario.default_params" class="scenario-params">
                        <div class="params-title">默认参数：</div>
                        <div class="params-json">
                          <pre>{{ JSON.stringify(selectedScenario.default_params, null, 2) }}</pre>
                        </div>
                        <div class="params-actions">
                          <el-button size="small" type="primary" @click="useScenarioParams">
                            使用此参数
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form>
        </div>

        <!-- 第三步：执行计划 -->
        <div v-show="currentStep === 2" class="step-panel">
          <el-form
            ref="scheduleFormRef"
            :model="formData"
            :rules="scheduleRules"
            label-width="120px"
            class="step-form"
          >
            <el-form-item label="执行方式" prop="execution_type">
              <el-radio-group v-model="formData.execution_type">
                <el-radio label="immediate">立即执行</el-radio>
                <el-radio label="scheduled">定时执行</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item
              v-if="formData.execution_type === 'scheduled'"
              label="执行时间"
              prop="scheduled_time"
            >
              <el-date-picker
                v-model="formData.scheduled_time"
                type="datetime"
                placeholder="选择执行时间"
                :disabled-date="disabledDate"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item label="自动销毁" prop="auto_destroy">
              <el-switch
                v-model="formData.auto_destroy"
                active-text="启用"
                inactive-text="禁用"
              />
              <div class="form-tip">
                启用后，故障注入完成时会自动销毁，恢复系统状态
              </div>
            </el-form-item>

            <el-form-item label="最大时长" prop="max_duration">
              <el-input-number
                v-model="formData.max_duration"
                :min="1"
                :max="86400"
                placeholder="秒"
              />
              <span class="input-suffix">秒（可选，超时自动终止）</span>
            </el-form-item>
          </el-form>
        </div>

        <!-- 第四步：确认提交 -->
        <div v-show="currentStep === 3" class="step-panel">
          <div class="confirm-panel">
            <h4>任务配置确认</h4>
            <p class="confirm-desc">请仔细检查以下配置信息，确认无误后点击"{{ submitButtonText }}"按钮</p>

            <!-- 基本信息表格 -->
            <div class="config-table-section">
              <h5>基本信息</h5>
              <el-table :data="basicInfoData" class="config-table" border>
                <el-table-column prop="label" label="配置项" width="150" />
                <el-table-column prop="value" label="配置值" />
              </el-table>
            </div>

            <!-- 故障配置表格 -->
            <div class="config-table-section">
              <h5>故障配置</h5>
              <el-table :data="faultConfigData" class="config-table" border>
                <el-table-column prop="label" label="配置项" width="150" />
                <el-table-column prop="value" label="配置值">
                  <template #default="{ row }">
                    <div v-if="row.key === 'fault_params'" class="fault-params-display">
                      <el-tag
                        v-for="(value, key) in formData.fault_params"
                        :key="key"
                        class="param-tag"
                        type="info"
                      >
                        {{ key }}: {{ value }}
                      </el-tag>
                    </div>
                    <span v-else>{{ row.value }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 执行计划表格 -->
            <div class="config-table-section">
              <h5>执行计划</h5>
              <el-table :data="executionConfigData" class="config-table" border>
                <el-table-column prop="label" label="配置项" width="150" />
                <el-table-column prop="value" label="配置值" />
              </el-table>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤导航 -->
      <div class="step-actions">
        <el-button v-if="currentStep > 0" @click="handlePrevStep">上一步</el-button>
        <el-button v-if="currentStep < 3" type="primary" @click="handleNextStep">下一步</el-button>
        <el-button
          v-if="currentStep === 3"
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          size="large"
        >
          {{ submitButtonText }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { useChaosTasksStore } from '@/store/business/chaos/tasks'
import { useChaosScenariosStore } from '@/store/business/chaos/scenarios'
import { useEnvironmentStore } from '@/store/business/environment/index'
import type { ChaosTaskCreate, ChaosTaskUpdate, ChaosScenario, Environment } from '@/types/api/chaos'
import {
  FAULT_CATEGORIES,
  getFaultTypeLabel,
  getFaultTypeDefaultParams,
  getFaultTypeTagType
} from '@/constants/chaos-fault-types'

const router = useRouter()
const route = useRoute()
const chaosTasksStore = useChaosTasksStore()
const chaosScenariosStore = useChaosScenariosStore()
const environmentStore = useEnvironmentStore()

// 模式检测
const isEditMode = computed(() => route.name === 'ChaosTaskEdit')
const isCopyMode = computed(() => !!route.query.copy)
const taskId = computed(() => isEditMode.value ? Number(route.params.id) : null)
const copyTaskId = computed(() => isCopyMode.value ? Number(route.query.copy) : null)

// 页面标题和描述
const pageTitle = computed(() => {
  if (isEditMode.value) return '编辑混沌测试任务'
  if (isCopyMode.value) return '复制混沌测试任务'
  return '创建混沌测试任务'
})

const pageDescription = computed(() => {
  if (isEditMode.value) return '修改故障注入参数，更新混沌测试任务配置'
  if (isCopyMode.value) return '基于现有任务创建新的混沌测试任务'
  return '配置故障注入参数，创建新的混沌测试任务'
})

const submitButtonText = computed(() => {
  if (submitting.value) {
    return isEditMode.value ? '保存中...' : '创建中...'
  }
  return isEditMode.value ? '保存任务' : '创建任务'
})

// 响应式数据
const currentStep = ref(0)
const submitting = ref(false)
const loading = ref(false)
const environments = ref<Environment[]>([])
const scenarios = ref<ChaosScenario[]>([])
const selectedScenario = ref<ChaosScenario | null>(null)
const selectedCategory = ref<string>('')

// JSON编辑器相关
const jsonParams = ref('')
const jsonValid = ref(true)

// 表单引用
const basicFormRef = ref()
const faultFormRef = ref()
const scheduleFormRef = ref()

// 表单数据
const formData = reactive<ChaosTaskCreate & { scheduled_time: string }>({
  name: '',
  description: '',
  env_ids: [],
  fault_type: '',
  fault_params: {},
  execution_type: 'immediate',
  scheduled_time: '',
  auto_destroy: true,
  max_duration: 300
})

// 表单验证规则
const basicRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  env_ids: [
    { required: true, type: 'array' as const, min: 1, message: '请至少选择一个环境', trigger: 'change' }
  ]
}

const faultRules = {
  fault_type: [
    { required: true, message: '请选择故障类型', trigger: 'change' }
  ]
}

const scheduleRules = {
  scheduled_time: [
    { required: true, message: '请选择执行时间', trigger: 'change' }
  ]
}

// 计算属性
// 故障分类映射 - 使用统一定义
const faultCategories = computed(() => {
  console.log('使用统一的故障分类定义')

  // 直接使用统一的分类定义，确保一致性
  const result = FAULT_CATEGORIES.map(category => ({
    key: category.key,
    label: category.label,
    faultTypes: category.faultTypes.map(faultType => ({
      key: faultType.key,
      label: faultType.label
    }))
  }))

  console.log('统一故障分类:', result)
  return result
})

// 根据选择的故障类型过滤场景
const filteredScenarios = computed(() => {
  if (!formData.fault_type) return []

  return scenarios.value.filter(s =>
    s.fault_type === formData.fault_type && s.is_active
  )
})

// 当前分类下的故障类型
const currentCategoryFaultTypes = computed(() => {
  if (!selectedCategory.value) return []
  const category = faultCategories.value.find(cat => cat.key === selectedCategory.value)
  return category?.faultTypes || []
})

// 按类型分组的场景（内置/自定义）
const groupedScenarios = computed(() => {
  const builtin = filteredScenarios.value.filter(s => s.is_builtin)
  const custom = filteredScenarios.value.filter(s => !s.is_builtin)

  return {
    builtin,
    custom,
    hasBuiltin: builtin.length > 0,
    hasCustom: custom.length > 0
  }
})

// 确认页面表格数据
const basicInfoData = computed(() => [
  { label: '任务名称', value: formData.name || '-' },
  { label: '任务描述', value: formData.description || '无' },
  { label: '目标环境', value: getEnvironmentNames(formData.env_ids) },
  { label: '环境数量', value: `${formData.env_ids.length} 个` }
])

const faultConfigData = computed(() => [
  { label: '故障类型', value: getFaultTypeLabel(formData.fault_type) },
  { label: '故障参数', value: '', key: 'fault_params' }
])

const executionConfigData = computed(() => {
  const data = [
    { label: '执行方式', value: getExecutionTypeLabel(formData.execution_type) },
    { label: '自动销毁', value: formData.auto_destroy ? '是' : '否' }
  ]

  if (formData.execution_type === 'scheduled') {
    data.splice(1, 0, { label: '执行时间', value: formData.scheduled_time || '-' })
  }

  if (formData.max_duration) {
    data.push({ label: '最大时长', value: `${formData.max_duration} 秒` })
  }

  return data
})



// 监听器
watch(() => formData.fault_params, () => {
  updateJsonParams()
}, { deep: true })

// 生命周期
onMounted(() => {
  loadEnvironments()
  loadScenarios()

  // 根据模式加载数据
  if (isEditMode.value && taskId.value) {
    loadTaskForEdit(taskId.value)
  } else if (isCopyMode.value && copyTaskId.value) {
    loadTaskForCopy(copyTaskId.value)
  } else if (route.query.scenario) {
    // 从场景创建任务
    loadScenarioForTask(Number(route.query.scenario))
  }
})

// 方法
const loadEnvironments = async () => {
  try {
    const result = await environmentStore.fetchEnvironments()
    if (result) {
      environments.value = result.records
    }
  } catch (error) {
    ElMessage.error('加载环境列表失败')
  }
}

const loadScenarios = async () => {
  try {
    // 获取所有活跃场景（内置+自定义）
    const allScenariosResult = await chaosScenariosStore.fetchScenarios({
      is_active: true,
      size: 100 // 获取更多场景
    })


    if (allScenariosResult && allScenariosResult.records && allScenariosResult.records.length > 0) {
      scenarios.value = allScenariosResult.records
    }
  } catch (error) {
    console.error('加载场景模板失败:', error)
    ElMessage.error('加载场景模板失败')
  }
}

const loadTaskForEdit = async (id: number) => {
  loading.value = true
  try {
    const task = await chaosTasksStore.fetchTask(id)
    // 填充表单数据
    Object.assign(formData, {
      name: task.name,
      description: task.description,
      env_ids: task.env_ids || [],
      fault_type: task.fault_type,
      fault_params: task.fault_params || {},
      execution_type: task.execution_type,
      scheduled_time: task.scheduled_time || '',
      auto_destroy: task.auto_destroy,
      max_duration: task.max_duration
    })

    // 根据故障类型设置分类
    setSelectedCategoryByFaultType(task.fault_type)
  } catch (error) {
    ElMessage.error('加载任务数据失败')
    router.push('/chaos/tasks')
  } finally {
    loading.value = false
  }
}

const loadTaskForCopy = async (id: number) => {
  loading.value = true
  try {
    const task = await chaosTasksStore.fetchTask(id)
    // 复制任务数据，但重置名称和状态
    Object.assign(formData, {
      name: `${task.name} - 副本`,
      description: task.description,
      env_ids: [...(task.env_ids || [])],
      fault_type: task.fault_type,
      fault_params: { ...(task.fault_params || {}) },
      execution_type: task.execution_type,
      auto_destroy: task.auto_destroy,
      max_duration: task.max_duration
    })
  } catch (error) {
    ElMessage.error('加载任务数据失败')
    router.push('/chaos/tasks')
  } finally {
    loading.value = false
  }
}

const loadScenarioForTask = async (scenarioId: number) => {
  loading.value = true
  try {
    const scenario = scenarios.value.find(s => s.id === scenarioId)
    if (scenario) {
      // 使用场景数据初始化表单
      formData.fault_type = scenario.fault_type
      formData.fault_params = { ...scenario.default_params }
      selectedScenario.value = scenario

      // 增加使用次数
      await chaosScenariosStore.useScenario(scenario.id)
    }
  } catch (error) {
    console.error('加载场景数据失败:', error)
  } finally {
    loading.value = false
  }
}

const setSelectedCategoryByFaultType = (faultType: string) => {
  // 根据故障类型找到对应的分类
  for (const category of faultCategories.value) {
    if (category.faultTypes.some((ft: any) => ft.key === faultType)) {
      selectedCategory.value = category.key
      break
    }
  }
}

const handleCategoryChange = () => {
  // 重置故障类型和参数
  formData.fault_type = ''
  selectedScenario.value = null

  // 清空现有参数
  Object.keys(formData.fault_params).forEach(key => {
    delete formData.fault_params[key]
  })
}

const handleFaultTypeChange = (faultType: string | number | boolean | undefined) => {
  console.log('故障类型变更:', faultType, '类型:', typeof faultType)

  // 确保faultType是字符串
  const faultTypeStr = String(faultType)

  // 重置故障参数和选中的模板
  selectedScenario.value = null

  // 清空现有参数
  Object.keys(formData.fault_params).forEach(key => {
    delete formData.fault_params[key]
  })

  // 使用统一定义的默认参数
  const defaultParams = getFaultTypeDefaultParams(faultTypeStr)

  console.log('设置默认参数:', defaultParams)
  Object.assign(formData.fault_params, defaultParams)
  console.log('当前故障参数:', formData.fault_params)
}

const handleSelectScenario = async (scenario: ChaosScenario) => {
  // 直接使用场景的故障类型
  formData.fault_type = scenario.fault_type

  // 清空现有参数
  Object.keys(formData.fault_params).forEach(key => {
    delete formData.fault_params[key]
  })

  // 获取场景详情
  try {
    const detailScenario = await chaosScenariosStore.fetchScenario(scenario.id)
    selectedScenario.value = detailScenario

    // 应用场景参数
    if (detailScenario.default_params) {
      Object.assign(formData.fault_params, detailScenario.default_params)
    } else {
      // 使用统一定义的默认参数作为后备
      const defaultParams = getFaultTypeDefaultParams(scenario.fault_type)
      Object.assign(formData.fault_params, defaultParams)
    }
  } catch (error) {
    console.error('获取场景详情失败:', error)
    ElMessage.error('获取场景详情失败')

    selectedScenario.value = scenario
    const defaultParams = getFaultTypeDefaultParams(scenario.fault_type)
    Object.assign(formData.fault_params, defaultParams)
  }

  // 设置对应的分类
  setSelectedCategoryByFaultType(scenario.fault_type)

  // 更新JSON显示
  updateJsonParams()

  // 增加使用次数
  try {
    await chaosScenariosStore.useScenario(scenario.id)
  } catch (error) {
    console.error('更新场景使用次数失败:', error)
  }
}

// 清除选择的场景
const clearSelectedScenario = () => {
  selectedScenario.value = null
  ElMessage.success('已清除模板选择')
}

// 使用场景参数
const useScenarioParams = () => {
  if (selectedScenario.value && selectedScenario.value.default_params) {
    // 清空现有参数
    Object.keys(formData.fault_params).forEach(key => {
      delete formData.fault_params[key]
    })

    // 设置新参数
    Object.assign(formData.fault_params, selectedScenario.value.default_params)

    // 更新JSON显示
    updateJsonParams()

    ElMessage.success('已应用模板参数')
  }
}

const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleNextStep = async () => {
  // 验证当前步骤
  let valid = true

  switch (currentStep.value) {
    case 0:
      valid = await basicFormRef.value?.validate().catch(() => false)
      break
    case 1:
      valid = await faultFormRef.value?.validate().catch(() => false)
      break
    case 2:
      if (formData.execution_type === 'scheduled') {
        valid = await scheduleFormRef.value?.validate().catch(() => false)
      }
      break
  }

  if (valid && currentStep.value < 3) {
    currentStep.value++
  }
}

const handleSubmit = async () => {
  submitting.value = true
  try {
    if (isEditMode.value && taskId.value) {
      // 编辑模式
      const submitData: ChaosTaskUpdate = {
        name: formData.name,
        description: formData.description,
        env_ids: formData.env_ids,
        fault_type: formData.fault_type,
        fault_params: formData.fault_params,
        auto_destroy: formData.auto_destroy,
        max_duration: formData.max_duration
      }

      // 只有在定时执行且有时间时才添加 scheduled_time
      if (formData.execution_type === 'scheduled' && formData.scheduled_time) {
        (submitData as any).scheduled_time = new Date(formData.scheduled_time)
      }

      await chaosTasksStore.updateTask(taskId.value, submitData)
      ElMessage.success('任务更新成功')
      router.push('/chaos/tasks')
    } else {
      // 创建模式
      const submitData: ChaosTaskCreate = {
        name: formData.name,
        description: formData.description,
        env_ids: formData.env_ids,
        fault_type: formData.fault_type,
        fault_params: formData.fault_params,
        execution_type: formData.execution_type,
        auto_destroy: formData.auto_destroy,
        max_duration: formData.max_duration
      }

      // 只有在定时执行且有时间时才添加 scheduled_time
      if (formData.execution_type === 'scheduled' && formData.scheduled_time) {
        submitData.scheduled_time = formData.scheduled_time
      }

      await chaosTasksStore.createTask(submitData)
      ElMessage.success('任务创建成功')
      router.push('/chaos/tasks')
    }
  } catch (error: any) {

    // 显示具体的错误信息
    let errorMessage = isEditMode.value ? '任务更新失败' : '任务创建失败'
    if (error?.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error?.message) {
      errorMessage = error.message
    }

    ElMessage.error(errorMessage)
    // 不跳转，允许用户修改
  } finally {
    submitting.value = false
  }
}

const handleCancel = async () => {
  const action = isEditMode.value ? '编辑' : '创建'
  try {
    await ElMessageBox.confirm(`确定要取消${action}任务吗？未保存的数据将丢失。`, '确认取消', {
      type: 'warning'
    })
    router.push('/chaos/tasks')
  } catch (error) {
    // 用户取消
  }
}

// 工具方法
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now()
}

const getEnvironmentNames = (envIds: number[]) => {
  if (!envIds || envIds.length === 0) return '-'
  const names = envIds.map(id => {
    const env = environments.value.find(e => e.id === id)
    return env?.name || `环境${id}`
  })
  return names.join(', ')
}





const getExecutionTypeLabel = (type: string | undefined) => {
  if (!type) return ''
  const labels: Record<string, string> = {
    immediate: '立即执行',
    scheduled: '定时执行'
  }
  return labels[type] || type
}

// 场景相关方法
const formatScenarioParams = (scenario: ChaosScenario): string => {
  if (scenario.default_params) {
    return JSON.stringify(scenario.default_params, null, 2)
  }
  return '暂无参数数据'
}

const isRequiredParam = (paramKey: string, scenario: ChaosScenario): boolean => {
  // 检查场景的required字段
  if ((scenario as any).required && Array.isArray((scenario as any).required)) {
    return (scenario as any).required.includes(paramKey)
  }

  // 如果没有required字段，检查param_schema中的required属性
  if ((scenario as any).param_schema && (scenario as any).param_schema[paramKey]) {
    return (scenario as any).param_schema[paramKey].required === true
  }

  // 默认为可选参数
  return false
}

// 参数操作方法
const resetParams = () => {
  Object.keys(formData.fault_params).forEach(key => {
    delete formData.fault_params[key]
  })
  updateJsonParams()
}



const formatJson = () => {
  try {
    const parsed = JSON.parse(jsonParams.value)
    jsonParams.value = JSON.stringify(parsed, null, 2)
    jsonValid.value = true
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化')
    jsonValid.value = false
  }
}

const handleJsonChange = () => {
  try {
    const parsed = JSON.parse(jsonParams.value)
    // 清空现有参数
    Object.keys(formData.fault_params).forEach(key => {
      delete formData.fault_params[key]
    })
    // 设置新参数
    Object.assign(formData.fault_params, parsed)
    jsonValid.value = true
  } catch (error) {
    jsonValid.value = false
    // 不显示错误消息，只更新状态
  }
}

const updateJsonParams = () => {
  jsonParams.value = JSON.stringify(formData.fault_params, null, 2)
  jsonValid.value = true
}
</script>

<style scoped>
.task-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
  color: rgb(var(--art-text-primary));
}

.header-desc {
  margin: 0;
  color: rgb(var(--art-text-secondary));
  font-size: 14px;
}

.form-container {
  background: rgb(var(--art-main-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 8px;
  padding: 30px;
  box-shadow: var(--art-card-shadow);
}

.step-content {
  margin: 40px 0;
  min-height: 400px;
}
.step-panel-fault-config{
  max-width: 1200px;
  margin: 0 auto;
}
.step-panel {
  max-width: 800px;
  margin: 0 auto;
}

.step-form {
  padding: 20px 0;
}

.form-tip {
  font-size: 12px;
  color: rgb(var(--art-text-gray-400));
  padding-left:  5px;
}
  
.input-suffix {
  margin-left: 10px;
  color: rgb(var(--art-text-gray-400));
  font-size: 14px;
}

/* 故障配置容器 - 左右分栏布局 */
.fault-config-container {
  display: flex;
  gap: 24px;
  margin-top: 30px;
  min-height: 600px;
}

/* 左侧参数配置区域 */
.fault-params-section {
  flex: 1;
  min-width: 0; /* 防止flex子项溢出 */
}

.fault-params-section .params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgb(var(--art-border-light));
}

.fault-params-section .params-header h4 {
  margin: 0;
  color: rgb(var(--art-text-primary));
  font-size: 16px;
  font-weight: 600;
}

.fault-params-section .params-actions {
  display: flex;
  gap: 8px;
}

/* 右侧模板选择区域 */
.scenario-templates-section {
  flex: 0 0 400px; /* 固定宽度400px */
  background: rgb(var(--art-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 8px;
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.scenario-templates-section .templates-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgb(var(--art-border-light));
}

.scenario-templates-section .templates-header h4 {
  margin: 0;
  color: rgb(var(--art-text-primary));
  font-size: 16px;
  font-weight: 600;
}

.selected-template-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.templates-content {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .fault-config-container {
    flex-direction: column;
  }

  .scenario-templates-section {
    flex: none;
    max-height: 400px;
  }
}

.fault-params {
  margin-top: 30px;
  padding: 20px;
  background: rgb(var(--art-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 6px;
}

.params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.params-header h4 {
  margin: 0;
  color: rgb(var(--art-text-primary));
}

.params-actions {
  display: flex;
  gap: 8px;
}

.json-editor-container {
  margin-bottom: 20px;
}

.json-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.json-info {
  margin-top: 15px;
}

.json-status {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.param-count {
  font-size: 12px;
  color: rgb(var(--art-text-gray-400));
}

.json-help {
  border: none;
  background: transparent;
}

.json-examples h5 {
  margin: 0 0 15px 0;
  color: rgb(var(--art-text-primary));
  font-size: 14px;
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.example-item {
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 6px;
  padding: 12px;
  background: rgb(var(--art-bg-color));
}

.example-item h6 {
  margin: 0 0 8px 0;
  color: rgb(var(--art-text-primary));
  font-size: 13px;
  font-weight: 500;
}

.example-item pre {
  margin: 0;
  font-size: 11px;
  line-height: 1.4;
  color: rgb(var(--art-text-secondary));
  background: transparent;
  border: none;
  padding: 0;
}

.no-scenario-tip {
  padding: 20px;
  text-align: center;
}

.scenario-examples {
  padding: 15px;
}

.current-scenario-info {
  margin-bottom: 20px;
  padding: 15px;
  background: rgb(var(--art-bg-color));
  border-radius: 6px;
  border: 1px solid rgb(var(--art-border-light));
}

.scenario-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.scenario-desc {
  margin: 0;
  font-size: 13px;
  color: rgb(var(--art-text-secondary));
  line-height: 1.4;
}

.example-content h6 {
  margin: 0 0 8px 0;
  color: rgb(var(--art-text-primary));
  font-size: 13px;
  font-weight: 500;
}

.scenario-params {
  background: rgb(var(--art-main-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 4px;
  padding: 12px;
  margin: 0 0 15px 0;
  font-size: 12px;
  line-height: 1.4;
  color: rgb(var(--art-text-primary));
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.param-schema {
  margin-top: 15px;
}

.schema-info {
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 4px;
  overflow: hidden;
}

.schema-header {
  display: grid;
  grid-template-columns: 120px 60px 80px 1fr;
  gap: 10px;
  padding: 10px 12px;
  background: rgb(var(--art-bg-color));
  border-bottom: 1px solid rgb(var(--art-border-light));
  font-size: 12px;
  font-weight: 500;
  color: rgb(var(--art-text-primary));
}

.schema-item {
  display: grid;
  grid-template-columns: 120px 60px 80px 1fr;
  gap: 10px;
  padding: 8px 12px;
  border-bottom: 1px solid rgb(var(--art-border-light));
  font-size: 12px;
  align-items: center;
  transition: background-color 0.2s;
}

.schema-item:last-child {
  border-bottom: none;
}

.schema-item:hover {
  background: rgb(var(--art-hoverColor));
}

.schema-item.required-param {
  background: rgba(var(--art-danger), 0.05);
}

.param-name {
  font-weight: 500;
  color: rgb(var(--art-text-primary));
  font-family: monospace;
}

.param-required {
  display: flex;
  justify-content: center;
}

.param-type {
  color: rgb(var(--art-primary));
  font-size: 11px;
  background: rgb(var(--art-bg-primary));
  padding: 2px 6px;
  border-radius: 3px;
  text-align: center;
}

.param-desc {
  color: rgb(var(--art-text-secondary));
  line-height: 1.3;
}

.scenario-templates {
  margin-top: 30px;
}

.scenario-templates h4 {
  margin: 0 0 15px 0;
  color: rgb(var(--art-text-primary));
}

.template-section {
  margin-bottom: 25px;
}

.template-section:last-child {
  margin-bottom: 0;
}

.template-section h5 {
  margin: 0 0 12px 0;
  color: rgb(var(--art-text-secondary));
  font-size: 14px;
  font-weight: 500;
  padding-left: 8px;
  border-left: 2px solid rgb(var(--art-border-light));
}

.template-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 在右侧区域中的模板网格 */
.scenario-templates-section .template-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-card {
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
  background: rgb(var(--art-main-bg-color));
}

/* 右侧区域中的模板卡片 */
.scenario-templates-section .template-card {
  padding: 12px;
  margin-bottom: 0;
}

.template-card:hover {
  border-color: rgb(var(--art-primary));
  box-shadow: 0 2px 8px rgba(var(--art-primary), 0.1);
}

.template-card.active {
  border-color: rgb(var(--art-primary));
  background: rgb(var(--art-bg-primary));
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.template-name {
  font-weight: 500;
  color: rgb(var(--art-text-primary));
  flex: 1;
  margin-right: 8px;
}

.template-tags {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
}

.template-card.builtin {
  border-left: 3px solid rgb(var(--art-success));
}

.template-card.custom {
  border-left: 3px solid rgb(var(--art-info));
}

.no-templates {
  text-align: center;
  padding: 40px 20px;
  color: rgb(var(--art-text-gray-400));
}

/* 选中模板详情 */
.selected-scenario-detail {
  margin-top: 20px;
  padding: 16px;
  background: rgb(var(--art-main-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 6px;
}

.selected-scenario-detail .detail-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgb(var(--art-border-light));
}

.selected-scenario-detail .detail-header h5 {
  margin: 0;
  color: rgb(var(--art-text-primary));
  font-size: 14px;
  font-weight: 600;
}

.selected-scenario-detail .scenario-info {
  margin-bottom: 16px;
}

.selected-scenario-detail .scenario-name {
  font-size: 14px;
  font-weight: 600;
  color: rgb(var(--art-text-primary));
  margin-bottom: 8px;
}

.selected-scenario-detail .scenario-type {
  display: flex;
  gap: 6px;
  margin-bottom: 8px;
}

.selected-scenario-detail .scenario-desc {
  font-size: 12px;
  color: rgb(var(--art-text-secondary));
  line-height: 1.4;
}

.selected-scenario-detail .scenario-params {
  margin-top: 12px;
}

.selected-scenario-detail .params-title {
  font-size: 12px;
  font-weight: 600;
  color: rgb(var(--art-text-primary));
  margin-bottom: 8px;
}

.selected-scenario-detail .params-json {
  background: rgb(var(--art-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
}

.selected-scenario-detail .params-json pre {
  margin: 0;
  font-size: 11px;
  line-height: 1.4;
  color: rgb(var(--art-text-primary));
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.selected-scenario-detail .params-actions {
  text-align: right;
}

.template-desc {
  color: rgb(var(--art-text-secondary));
  font-size: 13px;
  margin-bottom: 8px;
}

.template-usage {
  color: rgb(var(--art-text-gray-400));
  font-size: 12px;
}

.confirm-panel {
  padding: 20px;
}

.confirm-panel h4 {
  margin: 0 0 10px 0;
  color: rgb(var(--art-text-primary));
  font-size: 20px;
  font-weight: 600;
}

.confirm-desc {
  margin: 0 0 30px 0;
  color: rgb(var(--art-text-secondary));
  font-size: 14px;
  line-height: 1.5;
}

.config-table-section {
  margin-bottom: 30px;
}

.config-table-section:last-child {
  margin-bottom: 0;
}

.config-table-section h5 {
  margin: 0 0 15px 0;
  color: rgb(var(--art-text-primary));
  font-size: 16px;
  font-weight: 500;
  padding-left: 10px;
  border-left: 3px solid rgb(var(--art-primary));
}

.config-table {
  width: 100%;
}

.config-table :deep(.el-table__header) {
  background-color: rgb(var(--art-bg-color));
}

.config-table :deep(.el-table__header th) {
  background-color: rgb(var(--art-bg-color));
  color: rgb(var(--art-text-primary));
  font-weight: 500;
}

.config-table :deep(.el-table__body tr:hover > td) {
  background-color: rgb(var(--art-hoverColor));
}

.fault-params-display {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.param-tag {
  margin: 0;
}

.step-actions {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid rgb(var(--art-border-light));
}

.step-actions .el-button {
  margin: 0 10px;
}

/* 暗黑模式特殊适配 */
html.dark {
  .task-form {
    .form-container {
      background: rgb(var(--art-main-bg-color));
      border-color: rgb(var(--art-border-light));
    }

    .fault-params {
      background: rgb(var(--art-bg-color));
      border-color: rgb(var(--art-border-light));
    }

    .template-card {
      background: rgb(var(--art-main-bg-color));
      border-color: rgb(var(--art-border-light));

      &:hover {
        border-color: rgb(var(--art-primary));
        box-shadow: 0 2px 8px rgba(var(--art-primary), 0.2);
      }

      &.active {
        background: rgb(var(--art-bg-primary));
        border-color: rgb(var(--art-primary));
      }
    }

    .config-table {
      :deep(.el-table__header th) {
        background-color: rgb(var(--art-bg-color)) !important;
        border-color: rgb(var(--art-border-light));
      }

      :deep(.el-table__body td) {
        border-color: rgb(var(--art-border-light));
      }

      :deep(.el-table__border) {
        border-color: rgb(var(--art-border-light));
      }

      :deep(.el-table__body tr:hover > td) {
        background-color: rgb(var(--art-hoverColor)) !important;
      }
    }
  }
}
</style>
