"""
定时任务调度基础类
提供统一的调度管理接口和基础功能
"""
import logging
import asyncio
import inspect
from datetime import datetime, timedelta
from typing import Optional, Callable, Any, Dict, List, Union
from functools import wraps
from enum import Enum

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, EVENT_JOB_MISSED
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.database.connection import get_db

logger = logging.getLogger(__name__)


class ScheduleType(str, Enum):
    """调度类型枚举"""
    DATE = "date"        # 单次执行
    INTERVAL = "interval"  # 间隔执行
    CRON = "cron"        # Cron表达式


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"    # 等待执行
    RUNNING = "running"    # 正在执行
    SUCCESS = "success"    # 执行成功
    FAILED = "failed"      # 执行失败
    MISSED = "missed"      # 错过执行
    CANCELLED = "cancelled" # 已取消


class ScheduleTask:
    """调度任务封装类"""
    
    def __init__(
        self,
        task_id: str,
        name: str,
        func: Callable,
        task_type: str = "general",
        description: Optional[str] = None,
        max_instances: int = 1,
        misfire_grace_time: int = 300,
        args: Optional[tuple] = None,
        kwargs: Optional[dict] = None
    ):
        self.task_id = task_id
        self.name = name
        self.func = func
        self.task_type = task_type
        self.description = description
        self.max_instances = max_instances
        self.misfire_grace_time = misfire_grace_time
        self.args = args or ()
        self.kwargs = kwargs or {}
        
        # 自动检测函数路径
        self.function_path = f"{func.__module__}.{func.__name__}"
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'task_name': self.name,
            'task_type': self.task_type,
            'description': self.description,
            'function_path': self.function_path,
            'function_args': list(self.args),
            'function_kwargs': self.kwargs,
            'max_instances': self.max_instances,
            'misfire_grace_time': self.misfire_grace_time
        }


class ScheduleManager:
    """定时任务调度管理器"""
    
    def __init__(self):
        self._scheduler: Optional[AsyncIOScheduler] = None
        self._tasks: Dict[str, ScheduleTask] = {}
        self._is_initialized = False
        
    async def initialize(self):
        """初始化调度器"""
        if self._is_initialized:
            logger.warning("调度器已初始化")
            return
            
        try:
            # 先测试数据库连接和表是否存在
            logger.info(f"正在初始化调度器，数据库URL: {settings.DATABASE_URL}")

            # 配置作业存储（暂时使用内存存储，避免异步数据库兼容性问题）
            from apscheduler.jobstores.memory import MemoryJobStore
            jobstores = {
                'default': MemoryJobStore()
            }

            # TODO: 后续可以考虑使用Redis或者同步数据库连接
            logger.info("使用内存作业存储（重启后作业会丢失）")
            
            # 配置执行器
            executors = {
                'default': AsyncIOExecutor()
            }
            
            # 作业默认配置
            job_defaults = {
                'coalesce': False,  # 不合并作业
                'max_instances': 3,  # 默认最大实例数
                'misfire_grace_time': 300  # 错过执行的宽限时间（5分钟）
            }
            
            # 创建调度器
            self._scheduler = AsyncIOScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone='Asia/Shanghai'
            )
            
            # 添加事件监听器
            self._scheduler.add_listener(self._on_job_executed, EVENT_JOB_EXECUTED)
            self._scheduler.add_listener(self._on_job_error, EVENT_JOB_ERROR)
            self._scheduler.add_listener(self._on_job_missed, EVENT_JOB_MISSED)
            
            self._is_initialized = True
            logger.info("定时任务调度器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化调度器失败: {str(e)}")
            raise
    
    async def start(self):
        """启动调度器"""
        if not self._is_initialized:
            await self.initialize()
            
        if self._scheduler and not self._scheduler.running:
            self._scheduler.start()
            logger.info("定时任务调度器启动成功")
        else:
            logger.warning("调度器已在运行或未初始化")
    
    async def shutdown(self, wait: bool = True):
        """关闭调度器"""
        if self._scheduler and self._scheduler.running:
            self._scheduler.shutdown(wait=wait)
            logger.info("定时任务调度器已关闭")
    
    @property
    def is_running(self) -> bool:
        """检查调度器是否运行中"""
        try:
            return self._scheduler and self._scheduler.running and self._is_initialized
        except Exception:
            return False
    
    def _wrap_task_function(self, task: ScheduleTask) -> Callable:
        """包装任务函数，添加执行记录和错误处理"""
        @wraps(task.func)
        async def wrapper(*args, **kwargs):
            start_time = datetime.now()
            task_id = task.task_id
            
            # 记录任务开始
            await self._record_task_start(task_id, start_time)
            
            try:
                logger.info(f"开始执行任务: {task.name} ({task_id})")
                
                # 执行任务函数
                if inspect.iscoroutinefunction(task.func):
                    result = await task.func(*args, **kwargs)
                else:
                    result = task.func(*args, **kwargs)
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                # 记录任务成功
                await self._record_task_success(task_id, start_time, end_time, duration, result)
                
                logger.info(f"任务执行成功: {task.name} ({task_id}), 耗时: {duration:.2f}秒")
                return result
                
            except Exception as e:
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                # 记录任务失败
                await self._record_task_error(task_id, start_time, end_time, duration, str(e))
                
                logger.error(f"任务执行失败: {task.name} ({task_id}), 错误: {str(e)}")
                raise
        
        return wrapper
    
    async def _record_task_start(self, task_id: str, start_time: datetime):
        """记录任务开始执行"""
        try:
            async for db in get_db():
                # 这里可以记录到数据库
                # 暂时只记录日志
                logger.debug(f"任务开始记录: {task_id} at {start_time}")
                break
        except Exception as e:
            logger.error(f"记录任务开始失败: {str(e)}")
    
    async def _record_task_success(self, task_id: str, start_time: datetime, end_time: datetime, duration: float, result: Any):
        """记录任务执行成功"""
        try:
            async for db in get_db():
                # 这里可以记录到数据库
                # 暂时只记录日志
                logger.debug(f"任务成功记录: {task_id}, 耗时: {duration}秒")
                break
        except Exception as e:
            logger.error(f"记录任务成功失败: {str(e)}")
    
    async def _record_task_error(self, task_id: str, start_time: datetime, end_time: datetime, duration: float, error: str):
        """记录任务执行失败"""
        try:
            async for db in get_db():
                # 这里可以记录到数据库
                # 暂时只记录日志
                logger.debug(f"任务失败记录: {task_id}, 错误: {error}")
                break
        except Exception as e:
            logger.error(f"记录任务失败失败: {str(e)}")
    
    def _on_job_executed(self, event):
        """作业执行成功监听器"""
        logger.debug(f"作业执行成功: {event.job_id}")
    
    def _on_job_error(self, event):
        """作业执行错误监听器"""
        logger.error(f"作业执行失败: {event.job_id}, 错误: {event.exception}")
    
    def _on_job_missed(self, event):
        """作业错过执行监听器"""
        logger.warning(f"作业错过执行: {event.job_id}, 计划时间: {event.scheduled_run_time}")
    
    def add_date_task(
        self,
        task: ScheduleTask,
        run_date: datetime,
        replace_existing: bool = True
    ) -> str:
        """添加单次执行任务"""
        if not self._scheduler:
            raise RuntimeError("调度器未初始化")
        
        wrapped_func = self._wrap_task_function(task)
        
        job = self._scheduler.add_job(
            func=wrapped_func,
            trigger=DateTrigger(run_date=run_date),
            id=task.task_id,
            name=task.name,
            args=task.args,
            kwargs=task.kwargs,
            max_instances=task.max_instances,
            misfire_grace_time=task.misfire_grace_time,
            replace_existing=replace_existing
        )
        
        self._tasks[task.task_id] = task
        logger.info(f"添加单次任务: {task.name} ({task.task_id}), 执行时间: {run_date}")
        return job.id
    
    def add_interval_task(
        self,
        task: ScheduleTask,
        seconds: int = 0,
        minutes: int = 0,
        hours: int = 0,
        days: int = 0,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        replace_existing: bool = True
    ) -> str:
        """添加间隔执行任务"""
        if not self._scheduler:
            raise RuntimeError("调度器未初始化")
        
        wrapped_func = self._wrap_task_function(task)
        
        job = self._scheduler.add_job(
            func=wrapped_func,
            trigger=IntervalTrigger(
                seconds=seconds,
                minutes=minutes,
                hours=hours,
                days=days,
                start_date=start_date,
                end_date=end_date
            ),
            id=task.task_id,
            name=task.name,
            args=task.args,
            kwargs=task.kwargs,
            max_instances=task.max_instances,
            misfire_grace_time=task.misfire_grace_time,
            replace_existing=replace_existing
        )
        
        self._tasks[task.task_id] = task
        logger.info(f"添加间隔任务: {task.name} ({task.task_id}), 间隔: {days}天{hours}时{minutes}分{seconds}秒")
        return job.id
    
    def add_cron_task(
        self,
        task: ScheduleTask,
        year: Union[int, str] = None,
        month: Union[int, str] = None,
        day: Union[int, str] = None,
        week: Union[int, str] = None,
        day_of_week: Union[int, str] = None,
        hour: Union[int, str] = None,
        minute: Union[int, str] = None,
        second: Union[int, str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        replace_existing: bool = True
    ) -> str:
        """添加Cron表达式任务"""
        if not self._scheduler:
            raise RuntimeError("调度器未初始化")
        
        wrapped_func = self._wrap_task_function(task)
        
        job = self._scheduler.add_job(
            func=wrapped_func,
            trigger=CronTrigger(
                year=year,
                month=month,
                day=day,
                week=week,
                day_of_week=day_of_week,
                hour=hour,
                minute=minute,
                second=second,
                start_date=start_date,
                end_date=end_date
            ),
            id=task.task_id,
            name=task.name,
            args=task.args,
            kwargs=task.kwargs,
            max_instances=task.max_instances,
            misfire_grace_time=task.misfire_grace_time,
            replace_existing=replace_existing
        )
        
        self._tasks[task.task_id] = task
        logger.info(f"添加Cron任务: {task.name} ({task.task_id})")
        return job.id
    
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        if not self._scheduler:
            return False
        
        try:
            self._scheduler.remove_job(task_id)
            if task_id in self._tasks:
                del self._tasks[task_id]
            logger.info(f"移除任务: {task_id}")
            return True
        except Exception as e:
            logger.error(f"移除任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        if not self._scheduler:
            return False
        
        try:
            self._scheduler.pause_job(task_id)
            logger.info(f"暂停任务: {task_id}")
            return True
        except Exception as e:
            logger.error(f"暂停任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        if not self._scheduler:
            return False
        
        try:
            self._scheduler.resume_job(task_id)
            logger.info(f"恢复任务: {task_id}")
            return True
        except Exception as e:
            logger.error(f"恢复任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    def get_tasks(self) -> List[ScheduleTask]:
        """获取所有任务"""
        return list(self._tasks.values())
    
    def get_task(self, task_id: str) -> Optional[ScheduleTask]:
        """获取指定任务"""
        return self._tasks.get(task_id)
    
    def get_jobs(self) -> list:
        """获取所有作业"""
        if not self._scheduler:
            return []
        return self._scheduler.get_jobs()
    
    def get_job(self, job_id: str):
        """获取指定作业"""
        if not self._scheduler:
            return None
        return self._scheduler.get_job(job_id)
