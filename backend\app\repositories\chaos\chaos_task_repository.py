"""
混沌测试任务数据访问层
"""
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

from sqlalchemy import and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.repositories.base import BaseRepository
from app.models.chaos.chaos_task import ChaosTask
from app.schemas.chaos.chaos_task import ChaosTaskCreate, ChaosTaskUpdate, ChaosTaskQuery


class ChaosTaskRepository(BaseRepository[ChaosTask, ChaosTaskCreate, ChaosTaskUpdate]):
    """
    混沌测试任务数据访问层
    专注于任务相关的数据库操作
    """

    def __init__(self, db: AsyncSession):
        super().__init__(ChaosTask, db)

    async def get_with_environment(self, task_id: int) -> Optional[ChaosTask]:
        """获取任务 """
        return await self.get(task_id)

    async def get_with_executions(self, task_id: int) -> Optional[ChaosTask]:
        """ 获取任务及其执行记录"""
        query = (
            select(ChaosTask)
            .options(selectinload(ChaosTask.executions))
            .where(ChaosTask.id == task_id)
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def get_with_full_relations(self, task_id: int) -> Optional[ChaosTask]:
        """获取任务及其执行记录"""
        query = (
            select(ChaosTask)
            .options(selectinload(ChaosTask.executions))
            .where(ChaosTask.id == task_id)
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def get_by_status(self, status: str) -> List[ChaosTask]:
        """根据状态获取任务列表"""
        return await self.get_by_field("status", status, unique=False)

    async def get_running_tasks(self) -> List[ChaosTask]:
        """获取正在运行的任务"""
        return await self.get_by_status("running")

    async def get_by_env_id(self, env_id: int) -> List[ChaosTask]:
        """根据环境ID获取任务列表"""
        return await self.get_by_field("env_id", env_id, unique=False)

    async def get_by_fault_type(self, fault_type: str) -> List[ChaosTask]:
        """根据故障类型获取任务列表"""
        return await self.get_by_field("fault_type", fault_type, unique=False)

    async def search_tasks(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        keyword: Optional[str] = None,
        env_ids: Optional[List[int]] = None,
        fault_type: Optional[str] = None,
        status: Optional[str] = None,
        execution_type: Optional[str] = None,
        created_by: Optional[str] = None,
        order_by: str = "created_at",
        desc: bool = True
    ) -> Tuple[List[ChaosTask], int]:
        """搜索任务"""
        builder = self.query()

        # 关键词搜索
        if keyword:
            builder = builder.search(keyword, "name", "description")

        # 过滤条件
        filters = {}
        if fault_type:
            filters["fault_type"] = fault_type
        if status:
            filters["status"] = status
        if execution_type:
            filters["execution_type"] = execution_type
        if created_by:
            filters["created_by"] = created_by

        if filters:
            builder = builder.filter_by(**filters)

        # 环境ID列表过滤（需要特殊处理JSON字段）
        if env_ids:
            # 这里需要根据具体的数据库实现来处理JSON字段的查询
            # 暂时跳过，在实际使用中需要实现JSON字段的包含查询
            pass

        # 排序
        builder = builder.order_by(order_by, desc)

        # 分页查询
        return await builder.offset(skip).limit(limit).paginate(
            page=(skip // limit) + 1, per_page=limit
        )

    async def get_scheduled_tasks(self, before_time: datetime) -> List[ChaosTask]:
        """获取需要执行的定时任"""
        query = (
            select(ChaosTask)
            .where(
                and_(
                    ChaosTask.execution_type == "scheduled",
                    ChaosTask.status == "pending",
                    ChaosTask.scheduled_time <= before_time
                )
            )
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_tasks_by_host_id(self, host_id: int) -> List[ChaosTask]:
        """根据主机ID获取相关任务 """
        query = (
            select(ChaosTask)
            .where(ChaosTask.host_ids.contains([host_id]))
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def update_status(self, task_id: int, status: str, result: Optional[Dict[str, Any]] = None) -> bool:
        """更新任务状态"""
        task = await self.get(task_id)
        if not task:
            return False

        task.status = status
        if result:
            task.execution_result = result

        await self.db.commit()
        return True

    async def get_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        # 总任务数
        total_query = select(func.count(ChaosTask.id))
        total_result = await self.db.execute(total_query)
        total_count = total_result.scalar()

        # 按状态统计
        status_query = (
            select(ChaosTask.status, func.count(ChaosTask.id))
            .group_by(ChaosTask.status)
        )
        status_result = await self.db.execute(status_query)
        status_stats = {row[0]: row[1] for row in status_result.fetchall()}

        # 按故障类型统计
        fault_type_query = (
            select(ChaosTask.fault_type, func.count(ChaosTask.id))
            .group_by(ChaosTask.fault_type)
        )
        fault_type_result = await self.db.execute(fault_type_query)
        fault_type_stats = {row[0]: row[1] for row in fault_type_result.fetchall()}

        return {
            "total_count": total_count,
            "status_stats": status_stats,
            "fault_type_stats": fault_type_stats
        }

    async def search_tasks_with_query(self, query: ChaosTaskQuery) -> Tuple[List[ChaosTask], int]:
        """使用新的查询参数格式查询任务列表"""
        from sqlalchemy import or_, and_, func

        # 基础查询
        base_query = select(ChaosTask)

        # 筛选条件
        conditions = []

        # 关键词搜索（任务名称、描述）
        if query.keyword:
            conditions.append(
                or_(
                    ChaosTask.name.ilike(f"%{query.keyword}%"),
                    ChaosTask.description.ilike(f"%{query.keyword}%")
                )
            )

        # 其他筛选条件
        if query.fault_type:
            conditions.append(ChaosTask.fault_type == query.fault_type)
        if query.status:
            conditions.append(ChaosTask.status == query.status)
        if query.execution_type:
            conditions.append(ChaosTask.execution_type == query.execution_type)
        if query.created_by:
            conditions.append(ChaosTask.created_by == query.created_by)

        # 注意：时间范围筛选通过前端dateRange组件处理，后端不需要单独处理

        # 环境ID列表筛选（JSON字段包含查询）
        if query.env_ids:
            # 这里需要根据具体的数据库实现来处理JSON字段的查询
            # 暂时跳过，在实际使用中需要实现JSON字段的包含查询
            pass

        # 应用筛选条件
        if conditions:
            base_query = base_query.where(and_(*conditions))

        # 计算总条数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # 分页查询
        query_with_pagination = (
            base_query
            .offset(query.offset)
            .limit(query.size)
            .order_by(ChaosTask.created_at.desc())
        )

        result = await self.db.execute(query_with_pagination)
        items = result.scalars().all()

        return items, total
