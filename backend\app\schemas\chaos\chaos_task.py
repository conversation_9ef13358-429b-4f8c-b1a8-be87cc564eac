"""
混沌测试任务数据传输对象
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

from app.schemas.base import BaseResponseSchema, BaseQuery, BasePageResponse


class ChaosTaskBase(BaseModel):
    """混沌测试任务基础模型（包含公共字段）"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=1000, description="任务描述")
    env_ids: Optional[List[int]] = Field(None, description="目标环境ID列表")
    fault_type: Optional[str] = Field(None, description="故障类型")
    fault_params: Optional[Dict[str, Any]] = Field(None, description="故障参数配置")
    execution_type: Optional[str] = Field(None, description="执行类型")
    scheduled_time: Optional[datetime] = Field(None, description="定时执行时间")
    periodic_config: Optional[Dict[str, Any]] = Field(None, description="周期性执行配置")
    cron_expression: Optional[str] = Field(None, description="Cron表达式（用于cron类型）")
    auto_destroy: Optional[bool] = Field(None, description="是否自动销毁故障")
    max_duration: Optional[int] = Field(None, gt=0, description="最大执行时长(秒)")

    @validator('fault_type')
    def validate_fault_type(cls, v):
        """验证故障类型"""
        if v is not None:
            allowed_types = ['cpu', 'memory', 'network', 'disk', 'process', 'k8s', 'docker']
            if v not in allowed_types:
                raise ValueError(f'故障类型必须为: {", ".join(allowed_types)}')
        return v

    @validator('execution_type')
    def validate_execution_type(cls, v):
        """验证执行类型"""
        if v is not None:
            allowed_types = ['immediate', 'scheduled', 'periodic', 'cron']
            if v not in allowed_types:
                raise ValueError(f'执行类型必须为: {", ".join(allowed_types)}')
        return v


class ChaosTaskCreate(ChaosTaskBase):
    """创建混沌测试任务请求模型（必填字段）"""
    name: str = Field(..., min_length=1, max_length=100, description="任务名称")
    env_ids: List[int] = Field(..., min_items=1, description="目标环境ID列表")
    fault_type: str = Field(..., description="故障类型")
    fault_params: Dict[str, Any] = Field(..., description="故障参数配置")
    execution_type: str = Field(default="immediate", description="执行类型")
    auto_destroy: bool = Field(default=True, description="是否自动销毁故障")

    @validator('scheduled_time')
    def validate_scheduled_time(cls, v, values):
        """验证定时执行时间"""
        if values.get('execution_type') == 'scheduled' and not v:
            raise ValueError('定时执行必须设置执行时间')
        if v and v <= datetime.now():
            raise ValueError('执行时间必须大于当前时间')
        return v

    @validator('cron_expression')
    def validate_cron_expression(cls, v, values):
        """验证Cron表达式"""
        if v is not None and values.get('execution_type') == 'cron':
            # 简单的cron表达式格式验证
            parts = v.strip().split()
            if len(parts) not in [5, 6]:  # 支持5位或6位cron表达式
                raise ValueError('Cron表达式格式错误，应为5位或6位格式')

            # 可以添加更详细的cron表达式验证
            try:
                from croniter import croniter
                if not croniter.is_valid(v):
                    raise ValueError('Cron表达式格式无效')
            except ImportError:
                # 如果没有安装croniter，使用基本验证
                pass
        elif values.get('execution_type') == 'cron' and not v:
            raise ValueError('Cron执行类型必须提供cron_expression')
        return v

    @validator('periodic_config')
    def validate_periodic_config(cls, v, values):
        """验证周期性配置"""
        if values.get('execution_type') == 'periodic' and not v:
            raise ValueError('周期性执行必须设置周期配置')
        return v


class ChaosTaskUpdate(ChaosTaskBase):
    """更新混沌测试任务请求模型（可选字段）"""
    pass  # 继承ChaosTaskBase的所有可选字段


class ChaosTaskResponse(ChaosTaskBase, BaseResponseSchema):
    """混沌测试任务详情响应模型"""
    id: int = Field(..., description="任务ID")
    name: str = Field(..., description="任务名称")
    env_ids: List[int] = Field(..., description="目标环境ID列表")
    fault_type: str = Field(..., description="故障类型")
    fault_params: Dict[str, Any] = Field(..., description="故障参数配置")
    execution_type: str = Field(..., description="执行类型")
    status: str = Field(..., description="任务状态")
    auto_destroy: bool = Field(..., description="是否自动销毁故障")

    # 扩展字段
    execution_result: Optional[Dict[str, Any]] = Field(None, description="执行结果汇总")
    environment_names: Optional[List[str]] = Field(None, description="环境名称列表")
    env_count: int = Field(0, description="目标环境数量")

    # 状态信息
    can_execute: bool = Field(False, description="是否可以执行")
    can_pause: bool = Field(False, description="是否可以暂停")
    can_terminate: bool = Field(False, description="是否可以终止")

    class Config:
        from_attributes = True  # Pydantic V2 语法


class ChaosTaskListResponse(BaseModel):
    """任务列表项响应模式"""
    id: int = Field(description="任务ID")
    name: str = Field(description="任务名称")
    fault_type: str = Field(description="故障类型")
    status: str = Field(description="任务状态")
    env_ids: List[int] = Field(description="环境ID列表")
    environment_names: Optional[List[str]] = Field(description="环境名称列表")
    env_count: int = Field(description="目标环境数量")
    execution_type: str = Field(description="执行类型")
    created_at: Optional[datetime] = Field(description="创建时间")
    created_by: Optional[str] = Field(description="创建者")

    # 状态信息
    can_execute: bool = Field(description="是否可以执行")
    can_pause: bool = Field(description="是否可以暂停")
    can_terminate: bool = Field(description="是否可以终止")


class ChaosTaskQuery(BaseQuery):
    """混沌测试任务查询参数模型"""
    env_ids: Optional[List[int]] = Field(None, description="环境ID列表")
    fault_type: Optional[str] = Field(None, description="故障类型")
    status: Optional[str] = Field(None, description="任务状态")
    execution_type: Optional[str] = Field(None, description="执行类型")
    created_by: Optional[str] = Field(None, description="创建者")



class ChaosTaskPageResponse(BasePageResponse[ChaosTaskResponse]):
    """混沌测试任务列表分页响应模型"""
    pass



class ChaosTaskExecuteRequest(BaseModel):
    """
    执行任务请求模式
    """
    force: bool = Field(default=False, description="是否强制执行")
    override_params: Optional[Dict[str, Any]] = Field(None, description="覆盖参数")
    monitor_config: Optional[Dict[str, Any]] = Field(None, description="监控配置")


class ChaosTaskBatchRequest(BaseModel):
    """
    批量操作请求模式
    """
    task_ids: List[int] = Field(..., min_items=1, description="任务ID列表")
    action: str = Field(..., description="操作类型")
    
    @validator('action')
    def validate_action(cls, v):
        """验证操作类型"""
        allowed_actions = ['execute', 'pause', 'terminate', 'delete']
        if v not in allowed_actions:
            raise ValueError(f'操作类型必须为: {", ".join(allowed_actions)}')
        return v


class ChaosTaskStatistics(BaseModel):
    """
    任务统计信息
    """
    total_count: int = Field(description="总任务数")
    status_stats: Dict[str, int] = Field(description="状态统计")
    fault_type_stats: Dict[str, int] = Field(description="故障类型统计")
    recent_tasks: List[ChaosTaskListResponse] = Field(description="最近任务")


class ChaosBladeInstallRequest(BaseModel):
    """
    ChaosBlade安装请求
    """
    env_id: Optional[int] = Field(None, description="环境ID（可选，新架构中不再必需）")
    host_ids: Optional[List[int]] = Field(None, description="主机ID列表，为空则安装到环境所有主机")
    force_reinstall: bool = Field(default=False, description="是否强制重新安装")


class ChaosBladeStatusResponse(BaseModel):
    """
    ChaosBlade状态响应
    """
    host_id: int = Field(description="主机ID")
    host_name: Optional[str] = Field(description="主机名称")
    host_address: Optional[str] = Field(description="主机地址")
    installed: bool = Field(description="是否已安装")
    version: Optional[str] = Field(description="版本号")
    install_path: Optional[str] = Field(description="安装路径")
    last_check_time: Optional[datetime] = Field(description="最后检查时间")
    error_message: Optional[str] = Field(description="错误信息")
