#!/usr/bin/env python3
"""
测试Cron定时任务功能
"""
import asyncio
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_cron_task():
    """测试创建Cron定时任务"""
    try:
        from app.database.connection import get_db
        from app.services.chaos.chaos_task_service import ChaosTaskService
        from app.schemas.chaos.chaos_task import ChaosTaskCreate
        
        async for db in get_db():
            task_service = ChaosTaskService(db)
            
            # 创建Cron任务数据
            task_data = ChaosTaskCreate(
                name="Cron定时测试任务",
                description="每分钟执行一次的Cron测试任务",
                env_ids=[1],  # 假设环境ID 1存在
                fault_type="cpu",
                fault_params={
                    "cpu_percent": 30,
                    "duration": 10
                },
                execution_type="cron",
                cron_expression="*/1 * * * *",  # 每分钟执行一次
                monitor_config={
                    "enabled": False
                }
            )
            
            # 创建任务
            try:
                created_task = await task_service.create(task_data, "1")  # 系统用户
                
                logger.info(f"✅ 创建Cron定时任务成功:")
                logger.info(f"  任务ID: {created_task.id}")
                logger.info(f"  任务名称: {created_task.name}")
                logger.info(f"  执行类型: {created_task.execution_type}")
                logger.info(f"  Cron表达式: {created_task.cron_expression}")
                
                return created_task.id
                
            except Exception as e:
                logger.error(f"❌ 创建Cron任务失败: {str(e)}")
                return None
            
            break
            
    except Exception as e:
        logger.error(f"测试Cron任务失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

async def test_periodic_task():
    """测试创建周期性定时任务"""
    try:
        from app.database.connection import get_db
        from app.services.chaos.chaos_task_service import ChaosTaskService
        from app.schemas.chaos.chaos_task import ChaosTaskCreate
        
        async for db in get_db():
            task_service = ChaosTaskService(db)
            
            # 创建周期性任务数据
            task_data = ChaosTaskCreate(
                name="周期性定时测试任务",
                description="每2分钟执行一次的周期性测试任务",
                env_ids=[1],  # 假设环境ID 1存在
                fault_type="memory",
                fault_params={
                    "memory_percent": 40,
                    "duration": 15
                },
                execution_type="periodic",
                periodic_config={
                    "minutes": 2  # 每2分钟执行一次
                },
                monitor_config={
                    "enabled": False
                }
            )
            
            # 创建任务
            try:
                created_task = await task_service.create(task_data, "1")  # 系统用户
                
                logger.info(f"✅ 创建周期性定时任务成功:")
                logger.info(f"  任务ID: {created_task.id}")
                logger.info(f"  任务名称: {created_task.name}")
                logger.info(f"  执行类型: {created_task.execution_type}")
                logger.info(f"  周期配置: {created_task.periodic_config}")
                
                return created_task.id
                
            except Exception as e:
                logger.error(f"❌ 创建周期性任务失败: {str(e)}")
                return None
            
            break
            
    except Exception as e:
        logger.error(f"测试周期性任务失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

async def check_schedule_configs():
    """检查定时任务配置"""
    try:
        from app.database.connection import get_db
        from sqlalchemy import text
        
        async for db in get_db():
            query = text("""
                SELECT task_id, task_name, schedule_type, schedule_config, is_active
                FROM schedule_task_config 
                WHERE task_type = 'chaos_execution'
                ORDER BY created_at DESC
                LIMIT 10
            """)
            
            result = await db.execute(query)
            configs = result.fetchall()
            
            logger.info(f"当前定时任务配置数量: {len(configs)}")
            
            for config in configs:
                import json
                schedule_config = json.loads(config.schedule_config)
                logger.info(f"  {config.task_id}: {config.schedule_type}")
                logger.info(f"    名称: {config.task_name}")
                logger.info(f"    配置: {schedule_config}")
                logger.info(f"    激活: {config.is_active}")
                logger.info("")
            
            break
            
    except Exception as e:
        logger.error(f"检查定时任务配置失败: {str(e)}")

async def main():
    """主函数"""
    logger.info("=== 测试Cron和周期性定时任务 ===")
    
    # 测试Cron任务
    logger.info("\n--- 测试Cron任务 ---")
    cron_task_id = await test_cron_task()
    
    # 测试周期性任务
    logger.info("\n--- 测试周期性任务 ---")
    periodic_task_id = await test_periodic_task()
    
    # 检查配置
    logger.info("\n--- 检查定时任务配置 ---")
    await check_schedule_configs()
    
    if cron_task_id or periodic_task_id:
        logger.info("\n🎯 测试完成！请观察调度器日志，确认任务是否正常同步和执行。")
    else:
        logger.error("\n❌ 测试失败！")

if __name__ == "__main__":
    asyncio.run(main())
