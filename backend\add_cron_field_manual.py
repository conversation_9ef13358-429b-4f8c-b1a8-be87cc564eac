#!/usr/bin/env python3
"""
手动添加cron_expression字段
"""
import asyncio
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def add_cron_field():
    """添加cron_expression字段"""
    try:
        from app.database.connection import get_db
        from sqlalchemy import text
        
        async for db in get_db():
            # 检查字段是否已存在
            check_query = text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'dptest' 
                AND TABLE_NAME = 'chaos_tasks' 
                AND COLUMN_NAME = 'cron_expression'
            """)
            
            result = await db.execute(check_query)
            existing = result.fetchone()
            
            if existing:
                logger.info("cron_expression字段已存在")
                return
            
            # 添加字段
            add_query = text("""
                ALTER TABLE chaos_tasks 
                ADD COLUMN cron_expression VARCHAR(100) NULL COMMENT 'Cron表达式'
            """)
            
            await db.execute(add_query)
            await db.commit()
            
            logger.info("✅ 成功添加cron_expression字段")
            break
            
    except Exception as e:
        logger.error(f"添加字段失败: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(add_cron_field())
