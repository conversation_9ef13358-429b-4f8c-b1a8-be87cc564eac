"""
混沌测试任务数据模型
"""
from datetime import datetime
from typing import List, Dict, Any, Optional

from sqlalchemy import Column, String, Integer, Text, JSON, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class ChaosTask(BaseModel):
    """
    混沌测试任务模型
    管理故障注入任务的完整生命周期
    """
    __tablename__ = "chaos_tasks"

    # 基本信息
    name = Column(String(100), nullable=False, index=True, comment="任务名称")
    description = Column(Text, nullable=True, comment="任务描述")
    
    # 关联信息
    env_ids = Column(JSON, nullable=False, comment="目标环境ID列表")
    
    # 故障配置
    fault_type = Column(String(50), nullable=False, comment="故障类型：cpu/memory/network/disk/process/k8s")
    fault_params = Column(JSON, nullable=False, comment="故障参数配置")
    
    # 执行配置
    execution_type = Column(String(20), default="immediate", comment="执行类型：immediate/scheduled/periodic/cron")
    scheduled_time = Column(DateTime, nullable=True, comment="定时执行时间")
    periodic_config = Column(JSON, nullable=True, comment="周期性执行配置")
    cron_expression = Column(String(100), nullable=True, comment="Cron表达式")
    
    # 状态信息
    status = Column(String(20), default="pending", comment="任务状态：pending/running/paused/completed/failed/cancelled")
    execution_result = Column(JSON, nullable=True, comment="执行结果汇总")
    
    # 执行控制
    auto_destroy = Column(Boolean, default=True, comment="是否自动销毁故障")
    max_duration = Column(Integer, nullable=True, comment="最大执行时长(秒)")
    
    # 关系映射
    executions = relationship("ChaosExecution", back_populates="task", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<ChaosTask(id={self.id}, name={self.name}, status={self.status})>"

    @property
    def is_running(self) -> bool:
        """检查任务是否正在运行"""
        return self.status == "running"

    @property
    def is_completed(self) -> bool:
        """检查任务是否已完成"""
        return self.status in ["completed", "failed", "cancelled"]

    @property
    def can_execute(self) -> bool:
        """检查任务是否可以执行"""
        # 允许pending、paused、failed、completed状态的任务执行
        # 只有running状态的任务不能重复执行
        return self.status in ["pending", "paused", "failed", "completed"]

    @property
    def can_pause(self) -> bool:
        """检查任务是否可以暂停"""
        return self.status == "running"

    @property
    def can_terminate(self) -> bool:
        """检查任务是否可以终止"""
        return self.status in ["running", "paused"]

    @property
    def env_count(self) -> int:
        """获取目标环境数量"""
        if not self.env_ids:
            return 0
        return len(self.env_ids) if isinstance(self.env_ids, list) else 0

    def get_fault_param(self, key: str, default=None) -> Any:
        """获取故障参数值"""
        if not self.fault_params:
            return default
        return self.fault_params.get(key, default)

    def set_fault_param(self, key: str, value: Any) -> None:
        """设置故障参数值"""
        if not self.fault_params:
            self.fault_params = {}
        self.fault_params[key] = value

    def update_status(self, new_status: str, result: Optional[Dict[str, Any]] = None) -> None:
        """更新任务状态"""
        self.status = new_status
        if result:
            if not self.execution_result:
                self.execution_result = {}
            self.execution_result.update(result)

    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要信息"""
        return {
            "task_id": self.id,
            "task_name": self.name,
            "fault_type": self.fault_type,
            "env_count": self.env_count,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "execution_result": self.execution_result or {}
        }
