<template>
  <div class="execution-detail-page" v-loading="loading">
    <div v-if="execution">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <el-button @click="handleBack" :icon="ArrowLeft" class="back-btn">返回</el-button>
            <div class="header-info">
              <div class="title-section">
                <h1 class="page-title">执行记录详情</h1>
                <div class="title-meta">
                  <el-tag :type="getStatusTagType(execution.status)" size="large" class="status-tag">
                    <el-icon class="status-icon">
                      <component :is="getStatusIcon(execution.status)" />
                    </el-icon>
                    {{ getStatusLabel(execution.status) }}
                  </el-tag>
                  <span class="execution-id">ID: {{ execution.id }}</span>
                </div>
              </div>
              
            </div>
          </div>
          <div class="header-actions">
            <el-button
              v-if="execution.status === 'failed'"
              type="primary"
              @click="handleRetryExecution"
              :loading="retrying"
              size="large"
            >
              <el-icon><Refresh /></el-icon>
              重试执行
            </el-button>
            <el-button
              v-if="execution.is_running"
              type="warning"
              @click="handleCancelExecution"
              size="large"
            >
              <el-icon><Close /></el-icon>
              取消执行
            </el-button>
            <el-dropdown @command="handleDropdownCommand" trigger="click">
              <el-button size="large">
                更多操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="export">
                    <el-icon><Download /></el-icon>
                    导出详情
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon>
                    删除记录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 概览卡片 -->
        <div class="overview-section">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="overview-card task-card">
                <div class="card-icon">
                  <el-icon size="24"><Operation /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-title">关联任务</div>
                  <div class="card-value">
                    <el-link
                      v-if="execution.task_name"
                      type="primary"
                      @click="handleViewTask"
                      class="task-link"
                    >
                      {{ execution.task_name }}
                    </el-link>
                    <span v-else>Task-{{ execution.task_id }}</span>
                  </div>
                  <div class="card-meta">{{ (execution as any).environment_name || '未知环境' }}</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="overview-card host-card">
                <div class="card-icon">
                  <el-icon size="24"><Monitor /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-title">执行主机</div>
                  <div class="card-value">{{ execution.host_name || `Host-${execution.host_id}` }}</div>
                  <div class="card-meta">
                    <span v-if="execution.chaos_uid" class="uid-badge">
                      UID: {{ execution.chaos_uid.substring(0, 8) }}...
                    </span>
                    <span v-else class="no-uid">无UID</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="overview-card time-card">
                <div class="card-icon">
                  <el-icon size="24"><Timer /></el-icon>
                </div>
                <div class="card-content">
                  <div class="card-title">执行时长</div>
                  <div class="card-value">
                    <span v-if="execution.duration_seconds" class="duration-text">
                      {{ formatDuration(execution.duration_seconds) }}
                    </span>
                    <span v-else class="text-muted">计算中...</span>
                  </div>
                  <div class="card-meta">
                    {{ formatDateTime(execution.start_time) }} 开始
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 详细信息区域 -->
        <div class="details-section">
          <el-row :gutter="24">
            <!-- 基本信息 -->
            <el-col :span="12">
              <div class="detail-card">
                <div class="card-header">
                  <h3 class="card-title">
                    <el-icon><InfoFilled /></el-icon>
                    基本信息
                  </h3>
                </div>
                <div class="card-body">
                  <div class="info-grid">
                    <div class="info-row">
                      <div class="info-label">执行ID</div>
                      <div class="info-value">#{{ execution.id }}</div>
                    </div>
                    <div class="info-row">
                      <div class="info-label">ChaosBlade版本</div>
                      <div class="info-value">{{ execution.blade_version || 'V1.76' }}</div>
                    </div>
                    <div class="info-row">
                      <div class="info-label">重试次数</div>
                      <div class="info-value">
                        <el-tag v-if="execution.retry_count > 0" size="small" type="warning">
                          {{ execution.retry_count }} 次
                        </el-tag>
                        <span v-else>0 次</span>
                      </div>
                    </div>
                    <div class="info-row">
                      <div class="info-label">退出码</div>
                      <div class="info-value">
                        <el-tag
                          v-if="execution.exit_code !== null && execution.exit_code !== undefined"
                          size="small"
                          :type="execution.exit_code === 0 ? 'success' : 'danger'"
                        >
                          {{ execution.exit_code }}
                        </el-tag>
                        <span v-else class="text-muted">-</span>
                      </div>
                    </div>
                    <div class="info-row">
                      <div class="info-label">自动销毁</div>
                      <div class="info-value">
                        <el-tag :type="execution.is_auto_destroyed ? 'success' : 'info'" size="small">
                          {{ execution.is_auto_destroyed ? '已销毁' : '未销毁' }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>

            <!-- 时间信息 -->
            <el-col :span="12">
              <div class="detail-card">
                <div class="card-header">
                  <h3 class="card-title">
                    <el-icon><Clock /></el-icon>
                    时间信息
                  </h3>
                </div>
                <div class="card-body">
                  <div class="timeline-container">
                    <div class="timeline-item" :class="{ active: execution.created_at }">
                      <div class="timeline-dot"></div>
                      <div class="timeline-content">
                        <div class="timeline-title">创建时间</div>
                        <div class="timeline-time">{{ formatDateTime(execution.created_at) }}</div>
                        <div class="timeline-user">{{ execution.created_by || '系统' }}</div>
                      </div>
                    </div>
                    <div class="timeline-item" :class="{ active: execution.start_time }">
                      <div class="timeline-dot"></div>
                      <div class="timeline-content">
                        <div class="timeline-title">开始执行</div>
                        <div class="timeline-time">{{ formatDateTime(execution.start_time) }}</div>
                        <div class="timeline-user">自动触发</div>
                      </div>
                    </div>
                    <div class="timeline-item" :class="{ active: execution.end_time }">
                      <div class="timeline-dot"></div>
                      <div class="timeline-content">
                        <div class="timeline-title">执行完成</div>
                        <div class="timeline-time">{{ formatDateTime(execution.end_time) }}</div>
                        <div class="timeline-user">
                          <span v-if="execution.duration_seconds">
                            耗时 {{ formatDuration(execution.duration_seconds) }}
                          </span>
                          <span v-else>进行中...</span>
                        </div>
                      </div>
                    </div>
                    <div v-if="execution.destroy_time" class="timeline-item active">
                      <div class="timeline-dot"></div>
                      <div class="timeline-content">
                        <div class="timeline-title">故障销毁</div>
                        <div class="timeline-time">{{ formatDateTime(execution.destroy_time) }}</div>
                        <div class="timeline-user">{{ execution.updated_by || '自动销毁' }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
        </el-row>
        <!-- 执行日志 -->
        <el-card title="执行日志" class="log-card">
          <template #header>
            <div class="card-header">
              <span>执行日志</span>
              <el-button size="small" @click="refreshLogs">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <el-tabs v-model="activeLogTab" @tab-change="handleLogTabChange">
            <el-tab-pane label="执行输出" name="output">
              <div class="log-content">
                <el-input
                  v-model="logs.output"
                  type="textarea"
                  :rows="12"
                  readonly
                  placeholder="暂无执行输出"
                />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="错误信息" name="error">
              <div class="log-content">
                <el-input
                  v-model="logs.error"
                  type="textarea"
                  :rows="12"
                  readonly
                  placeholder="暂无错误信息"
                />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="执行命令" name="command">
              <div class="log-content">
                <el-input
                  v-model="logs.command"
                  type="textarea"
                  :rows="12"
                  readonly
                  placeholder="暂无执行命令"
                />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="销毁输出" name="destroy">
              <div class="log-content">
                <el-input
                  v-model="logs.destroy"
                  type="textarea"
                  :rows="12"
                  readonly
                  placeholder="暂无销毁输出"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
          
          <div class="log-actions">
            <el-button @click="handleCopyLog">复制当前日志</el-button>
            <el-button @click="handleDownloadLog">下载日志文件</el-button>
          </div>
        </el-card>

        <!-- 实时监控 -->
        <el-card v-if="execution.is_running" title="实时监控" class="monitor-card">
          <template #header>
            <div class="card-header">
              <span>实时监控</span>
              <el-button size="small" @click="refreshMonitorData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="monitor-content" v-if="monitorData">
            <div class="monitor-progress">
              <div class="progress-info">
                <span class="progress-label">执行进度</span>
                <span class="progress-value">{{ monitorData.progress.toFixed(1) }}%</span>
              </div>
              <el-progress
                :percentage="monitorData.progress"
                :status="getProgressStatus(monitorData.status)"
              />
            </div>
            
            <div class="monitor-details">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <span class="label">当前步骤：</span>
                    <span class="value">{{ monitorData.current_step }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">已执行时长：</span>
                    <span class="value">{{ formatDuration(monitorData.elapsed_seconds) }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item" v-if="monitorData.estimated_remaining_seconds">
                    <span class="label">预计剩余：</span>
                    <span class="value">{{ formatDuration(monitorData.estimated_remaining_seconds) }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">最后更新：</span>
                    <span class="value">{{ formatDateTime(monitorData.last_update_time) }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  ArrowDown,
  Refresh,
  Close,
  Download,
  Delete,
  Operation,
  Monitor,
  Timer,
  InfoFilled,
  Clock,
  CircleCheck,
  CircleClose,
  Loading,
  Warning
} from '@element-plus/icons-vue'
import { useChaosExecutionsStore } from '@/store/business/chaos/executions'
import type { ChaosExecution } from '@/types/api/chaos'

const router = useRouter()
const route = useRoute()
const chaosExecutionsStore = useChaosExecutionsStore()

// 响应式数据
const loading = ref(false)
const retrying = ref(false)
const execution = ref<ChaosExecution | null>(null)
const activeLogTab = ref('output')
const monitorData = ref<any>(null)

// 日志数据
const logs = reactive<Record<string, string>>({
  output: '',
  error: '',
  command: '',
  destroy: ''
})

// 计算属性
const executionId = computed(() => Number(route.params.id))

// 生命周期
onMounted(() => {
  loadExecutionDetail()
  loadAllLogs()
  
  // 如果是运行中状态，加载监控数据
  if (execution.value?.is_running) {
    loadMonitorData()
  }
})

// 方法
const loadExecutionDetail = async () => {
  loading.value = true
  try {
    execution.value = await chaosExecutionsStore.fetchExecution(executionId.value)
  } catch (error) {
    ElMessage.error('加载执行记录详情失败')
    router.push('/chaos/executions')
  } finally {
    loading.value = false
  }
}

const loadAllLogs = async () => {
  const logTypes = ['output', 'error', 'command', 'destroy']
  
  for (const logType of logTypes) {
    try {
      const response = await chaosExecutionsStore.getExecutionLog(executionId.value, logType)
      logs[logType] = response.content || ''
    } catch (error) {
      logs[logType] = ''
    }
  }
}

const loadMonitorData = async () => {
  try {
    monitorData.value = await chaosExecutionsStore.getExecutionMonitor(executionId.value)
  } catch (error) {
    console.error('加载监控数据失败:', error)
  }
}

const refreshLogs = () => {
  loadAllLogs()
}

const refreshMonitorData = () => {
  loadMonitorData()
}

const handleBack = () => {
  router.push('/chaos/executions')
}

// 状态相关方法
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'success':
      return CircleCheck
    case 'failed':
      return CircleClose
    case 'running':
      return Loading
    case 'pending':
      return Timer
    default:
      return Warning
  }
}

const handleViewTask = () => {
  if (execution.value) {
    router.push(`/chaos/tasks`)
  }
}

const handleRetryExecution = async () => {
  retrying.value = true
  try {
    await chaosExecutionsStore.retryExecution(executionId.value, {})
    ElMessage.success('重试执行成功')
    await loadExecutionDetail()
    await loadAllLogs()
  } catch (error) {
    ElMessage.error('重试执行失败')
  } finally {
    retrying.value = false
  }
}

const handleCancelExecution = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消执行吗？正在运行的故障注入将被停止。',
      '确认取消',
      { type: 'warning' }
    )
    
    await chaosExecutionsStore.cancelExecution(executionId.value)
    ElMessage.success('执行已取消')
    await loadExecutionDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消执行失败')
    }
  }
}

const handleDropdownCommand = (command: string) => {
  switch (command) {
    case 'export':
      handleExportExecution()
      break
    case 'delete':
      handleDeleteExecution()
      break
  }
}

const handleExportExecution = () => {
  if (!execution.value) return
  
  const exportData = {
    ...execution.value,
    logs: logs
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chaos-execution-${execution.value.id}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('执行记录已导出')
}

const handleDeleteExecution = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除执行记录 ${executionId.value} 吗？此操作不可恢复。`,
      '确认删除',
      { type: 'warning' }
    )
    
    await chaosExecutionsStore.deleteExecution(executionId.value)
    ElMessage.success('执行记录删除成功')
    router.push('/chaos/executions')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除执行记录失败')
    }
  }
}

const handleLogTabChange = (tabName: string | number) => {
  activeLogTab.value = String(tabName)
}

const handleCopyLog = () => {
  const currentLog = logs[activeLogTab.value]
  if (currentLog) {
    navigator.clipboard.writeText(currentLog)
    ElMessage.success('日志已复制到剪贴板')
  } else {
    ElMessage.warning('当前日志为空')
  }
}

const handleDownloadLog = () => {
  const currentLog = logs[activeLogTab.value]
  if (!currentLog) {
    ElMessage.warning('当前日志为空')
    return
  }
  
  const blob = new Blob([currentLog], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `execution-${executionId.value}-${activeLogTab.value}.log`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('日志文件已下载')
}

// 工具方法
const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    running: '运行中',
    success: '成功',
    failed: '失败',
    cancelled: '已取消'
  }
  return labels[status] || status
}

const getStatusTagType = (status: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' | undefined => {
  const types: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    pending: 'info',
    running: 'primary',
    success: 'success',
    failed: 'danger'
  }
  return types[status]
}

const getProgressStatus = (status: string) => {
  if (status === 'success') return 'success'
  if (status === 'failed') return 'exception'
  return undefined
}

const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${hours}时${minutes}分${secs}秒`
}
</script>

<style scoped>
.execution-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.header-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #666;
}

.meta-item {
  display: flex;
  align-items: center;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  height: 100%;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.info-item .label {
  width: 150px;
  color: #666;
  flex-shrink: 0;
}

.info-item .value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

.text-muted {
  color: #999;
}

.uid-text {
  font-family: monospace;
  font-size: 12px;
}

.host-card,
.config-card,
.log-card,
.monitor-card {
  margin-top: 20px;
}

.host-info,
.fault-config {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 15px;
}

.host-info pre,
.fault-config pre {
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.log-content {
  margin-bottom: 15px;
}

.log-actions {
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

.monitor-content {
  padding: 10px 0;
}

.monitor-progress {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-label {
  font-weight: 500;
  color: #333;
}

.progress-value {
  font-weight: 600;
  color: #409eff;
}

.monitor-details {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.detail-item .label {
  width: 100px;
  color: #666;
  flex-shrink: 0;
}

.detail-item .value {
  color: #333;
  flex: 1;
}

/* 新增现代化样式 */
.execution-detail-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px 32px;
}

.back-btn {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #f8f9fa;
  border-color: #409eff;
  color: #409eff;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: -0.5px;
  margin: 0;
}

.title-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
}

.status-icon {
  font-size: 16px;
}

.execution-id {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 主要内容区域 */
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 32px;
}

/* 概览卡片 */
.overview-section {
  margin-bottom: 32px;
}

.overview-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  align-items: center;
  gap: 16px;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.task-card .card-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.host-card .card-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.time-card .card-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

.card-value {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.card-meta {
  font-size: 12px;
  color: #9ca3af;
}

.task-link {
  font-size: 18px;
  font-weight: 700;
  text-decoration: none;
}

.task-link:hover {
  text-decoration: underline;
}

.uid-badge {
  background: #f3f4f6;
  color: #374151;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-family: monospace;
}

.no-uid {
  color: #9ca3af;
}

.duration-text {
  font-weight: 700;
  color: #059669;
}

/* 详细信息区域 */
.details-section {
  margin-bottom: 32px;
}

.detail-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
  background: #fafbfc;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.card-body {
  padding: 24px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 600;
}

/* 时间线样式 */
.timeline-container {
  position: relative;
  padding-left: 24px;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e5e7eb;
}

.timeline-item {
  position: relative;
  margin-bottom: 24px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -20px;
  top: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #e5e7eb;
  border: 3px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-item.active .timeline-dot {
  background: #10b981;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 2px;
}

.timeline-user {
  font-size: 12px;
  color: #9ca3af;
}
</style>
