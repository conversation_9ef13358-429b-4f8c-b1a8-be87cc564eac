# 前端错误处理指南

## 概述

本指南介绍如何在前端项目中使用新的统一错误码体系，包括RESTful API调用、错误处理和用户提示。

## 核心特性

1. **统一错误码体系** - 与后端保持一致的5位数字错误码
2. **智能错误提示** - 根据错误类型自动选择提示方式
3. **RESTful API支持** - 支持新的RESTful响应格式
4. **向后兼容** - 同时支持新旧API格式
5. **多语言支持** - 中英文错误消息

## 错误码体系

### 错误码格式
- **格式**: `MTTSS` (5位数字)
- **M**: 模块编码 (1=用户, 2=环境, 3=混沌测试, 4=数据工厂, 5=模型配置, 9=系统)
- **TT**: 类型编码 (01=验证, 02=权限, 03=不存在, 04=业务逻辑, 05=系统)
- **SS**: 序号编码 (01-99)

### 错误类型
- **验证错误** (x01xx) - 表单验证、参数错误 → 消息提示
- **权限错误** (x02xx) - 认证失败、权限不足 → 警告通知
- **资源错误** (x03xx) - 资源不存在 → 信息提示
- **业务错误** (x04xx) - 业务逻辑冲突 → 错误通知
- **系统错误** (x05xx) - 服务器错误 → 错误通知(含请求ID)

## API调用方式

### RESTful API (推荐)
```typescript
import { api } from '@/utils/http'

// 获取数据
const userData = await api.restful.get<UserResponse>({
  url: '/api/users/123'
})

// 创建数据
const newUser = await api.restful.post<UserResponse>({
  url: '/api/users',
  data: { name: 'John', email: '<EMAIL>' }
})

// 更新数据
const updatedUser = await api.restful.put<UserResponse>({
  url: '/api/users/123',
  data: { name: 'John Doe' }
})

// 删除数据 (无返回值)
await api.restful.delete({
  url: '/api/users/123'
})

// 分页查询
const userList = await api.restful.get<RestfulPaginationResponse<UserResponse>>({
  url: '/api/users/list',
  params: { current: 1, size: 20 }
})
```

### 传统API (向后兼容)
```typescript
// 仍然支持旧格式
const response = await api.get<APIResponse<UserResponse>>({
  url: '/api/users/123'
})
const userData = response.data
```

## 错误处理方式

### 1. 自动错误处理 (推荐)
```typescript
import { api } from '@/utils/http'

try {
  const user = await api.restful.get<UserResponse>({
    url: '/api/users/123'
  })
  // 成功处理
} catch (error) {
  // 错误会自动显示，无需手动处理
  console.log('操作失败')
}
```

### 2. 静默错误处理
```typescript
try {
  const user = await api.restful.get<UserResponse>({
    url: '/api/users/123',
    silentError: true  // 不显示错误提示
  })
} catch (error) {
  // 手动处理错误
  if (error.errorCode === 10301) {
    // 用户不存在
    showCustomMessage('用户不存在，请检查用户ID')
  }
}
```

### 3. 自定义错误处理
```typescript
import { handleError, handleErrorByCode } from '@/utils/errorHandler'

try {
  const user = await api.restful.get<UserResponse>({
    url: '/api/users/123',
    showErrorMessage: false
  })
} catch (error) {
  // 使用统一错误处理工具
  handleError(error, {
    customMessage: '获取用户信息失败',
    notificationType: 'notification'
  })
}
```

### 4. 根据错误码处理
```typescript
import { ErrorCode, isAuthError, getErrorType } from '@/constants/errorCodes'

try {
  const result = await api.restful.post({ url: '/api/users', data: userData })
} catch (error) {
  if (error.errorCode === ErrorCode.USER_ALREADY_EXISTS) {
    // 用户已存在
    ElMessage.warning('用户名已存在，请选择其他用户名')
  } else if (isAuthError(error.errorCode)) {
    // 认证错误，跳转登录
    router.push('/login')
  } else {
    // 其他错误，使用默认处理
    handleErrorByCode(error.errorCode, error.message)
  }
}
```

## 错误提示类型

### 自动选择提示方式
系统会根据错误类型自动选择最合适的提示方式：

- **验证错误** → `ElMessage.error()` (可关闭的消息)
- **权限错误** → `ElNotification.warning()` (警告通知)
- **资源不存在** → `ElMessage.info()` (信息提示)
- **业务错误** → `ElNotification.error()` (错误通知)
- **系统错误** → `ElNotification.error()` (错误通知，包含请求ID)

### 手动指定提示方式
```typescript
import { handleError } from '@/utils/errorHandler'

// 强制使用消息提示
handleError(error, { notificationType: 'message' })

// 强制使用通知提示
handleError(error, { notificationType: 'notification' })

// 自动选择 (默认)
handleError(error, { notificationType: 'auto' })
```

## 表单验证错误处理

```typescript
import { handleFormError } from '@/utils/errorHandler'

const submitForm = async () => {
  try {
    await api.restful.post({
      url: '/api/users',
      data: formData.value
    })
    ElMessage.success('创建成功')
  } catch (error) {
    // 自动处理表单验证错误
    handleFormError(error, formRef.value)
  }
}
```

## 错误码常量使用

```typescript
import { ErrorCode, ErrorType, getErrorType, isAuthError } from '@/constants/errorCodes'

// 检查特定错误码
if (error.errorCode === ErrorCode.USER_NOT_FOUND) {
  // 处理用户不存在
}

// 检查错误类型
const errorType = getErrorType(error.errorCode)
if (errorType === ErrorType.VALIDATION) {
  // 处理验证错误
}

// 检查是否为认证错误
if (isAuthError(error.errorCode)) {
  // 处理认证错误
}
```

## 类型定义

### 错误响应类型
```typescript
// 新格式错误响应
interface ErrorResponse {
  message: string
  error_code: number
  http_code: number
  details?: any
  timestamp: string
  request_id?: string
}

// RESTful分页响应
interface RestfulPaginationResponse<T> {
  records: T[]
  total: number
  current: number
  size: number
}
```

### HTTP错误类型
```typescript
// 增强的HttpError类
class HttpError extends Error {
  public readonly code: number
  public readonly errorCode?: number  // 业务错误码
  public readonly httpCode?: number   // HTTP状态码
  public readonly requestId?: string  // 请求ID
  
  // 便捷方法
  getErrorType(): ErrorType | null
  getErrorModule(): string | null
  isAuthError(): boolean
  isSystemError(): boolean
}
```

## 最佳实践

### 1. API调用
- 优先使用 `api.restful.*` 方法调用新接口
- 对于不需要错误提示的场景，使用 `silentError: true`
- 对于需要自定义错误处理的场景，使用 `showErrorMessage: false`

### 2. 错误处理
- 让系统自动处理大部分错误，减少重复代码
- 只对特殊业务场景进行自定义错误处理
- 使用错误码常量而不是硬编码数字

### 3. 用户体验
- 验证错误使用消息提示，不打断用户操作
- 权限错误使用通知，提醒用户注意
- 系统错误提供请求ID，便于问题追踪

### 4. 调试和监控
- 保持错误日志记录，便于问题排查
- 在开发环境显示详细错误信息
- 在生产环境隐藏敏感错误信息

## 迁移指南

### 从旧API迁移到RESTful API
```typescript
// 旧写法
const response = await api.get<APIResponse<UserResponse>>({
  url: '/api/users/123'
})
if (response.success) {
  const user = response.data
}

// 新写法
const user = await api.restful.get<UserResponse>({
  url: '/api/users/123'
})
```

### 错误处理迁移
```typescript
// 旧写法
try {
  const response = await api.get({ url: '/api/users' })
  if (!response.success) {
    ElMessage.error(response.message)
    return
  }
} catch (error) {
  ElMessage.error('请求失败')
}

// 新写法
try {
  const users = await api.restful.get({ url: '/api/users' })
  // 成功处理
} catch (error) {
  // 错误自动处理，无需手动显示
}
```

## 常见问题

### Q: 如何禁用自动错误提示？
A: 在请求配置中设置 `silentError: true` 或 `showErrorMessage: false`

### Q: 如何自定义错误提示内容？
A: 使用 `handleError()` 函数的 `customMessage` 选项

### Q: 如何处理特定的错误码？
A: 使用 `error.errorCode` 判断或使用 `handleErrorByCode()` 函数

### Q: 如何在表单中处理验证错误？
A: 使用 `handleFormError()` 函数，它会自动处理字段级别的错误

### Q: 新旧API格式可以混用吗？
A: 可以，系统会自动识别响应格式并进行相应处理
