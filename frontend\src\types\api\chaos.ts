/**
 * 混沌测试模块类型定义
 */

// 导入通用API类型
import type { PageResponse } from './common'

// ==================== 混沌任务相关类型 ====================

// 执行类型枚举
export type ExecutionType = 'immediate' | 'scheduled' | 'periodic' | 'cron'

// 周期配置接口
export interface PeriodicConfig {
  minutes?: number
  hours?: number
  days?: number
}

export interface ChaosTask {
  id: number
  name: string
  description?: string
  env_ids: number[]
  fault_type: string
  fault_params: Record<string, any>
  execution_type: ExecutionType
  scheduled_time?: string
  periodic_config?: PeriodicConfig
  cron_expression?: string
  status: string
  execution_result?: Record<string, any>
  auto_destroy: boolean
  max_duration?: number
  created_at?: string
  updated_at?: string
  created_by?: string
  updated_by?: string
  environment_names?: string[]
  env_count: number
  can_execute: boolean
  can_pause: boolean
  can_terminate: boolean
}

export interface ChaosTaskCreate {
  name: string
  description?: string
  env_ids: number[]
  fault_type: string
  fault_params: Record<string, any>
  execution_type?: ExecutionType
  scheduled_time?: string
  periodic_config?: PeriodicConfig
  cron_expression?: string
  auto_destroy?: boolean
  max_duration?: number
}

export interface ChaosTaskUpdate {
  name?: string
  description?: string
  env_ids?: number[]
  fault_type?: string
  fault_params?: Record<string, any>
  execution_type?: ExecutionType
  scheduled_time?: string
  periodic_config?: PeriodicConfig
  cron_expression?: string
  auto_destroy?: boolean
  max_duration?: number
}

export interface ChaosTaskSearchParams {
  keyword?: string
  env_ids?: number[]
  fault_type?: string
  status?: string
  execution_type?: string
  created_by?: string
  created_to?: string
  page?: number
  size?: number
  order_by?: string
  desc?: boolean
}

export interface ChaosTaskExecuteRequest {
  force?: boolean
  override_params?: Record<string, any>
}

export interface ChaosTaskBatchRequest {
  task_ids: number[]
  action: string
}

export interface ChaosTaskStatistics {
  total_count: number
  status_stats: Record<string, number>
  fault_type_stats: Record<string, number>
  recent_tasks: ChaosTask[]
}

// ==================== 故障场景相关类型 ====================

export interface ChaosScenario {
  id: number
  name: string
  fault_type: string
  description?: string
  default_params: Record<string, any>
  param_schema: Record<string, any>
  is_builtin: boolean
  is_active: boolean
  category?: string
  usage_count: number
  tags?: string
  created_at?: string
  updated_at?: string
  created_by?: string
  updated_by?: string
  tag_list: string[]
}

export interface ChaosScenarioCreate {
  name: string
  fault_type: string
  description?: string
  default_params: Record<string, any>
  param_schema?: Record<string, any>
  category?: string
  tags?: string
}

export interface ChaosScenarioUpdate {
  name?: string
  description?: string
  default_params?: Record<string, any>
  param_schema?: Record<string, any>
  is_active?: boolean
  category?: string
  tags?: string
}

export interface ChaosScenarioSearchParams {
  keyword?: string
  fault_type?: string
  category?: string
  is_builtin?: boolean
  is_active?: boolean
  tags?: string[]
  page?: number
  size?: number
  current?: number
  total?: number
  order_by?: string
  desc?: boolean
}

export interface ChaosScenarioValidateRequest {
  scenario_id: number
  params: Record<string, any>
}

export interface ChaosScenarioImportRequest {
  scenarios: ChaosScenarioTemplate[]
  overwrite_existing?: boolean
}

export interface ChaosScenarioTemplate {
  name: string
  fault_type: string
  description: string
  category: string
  default_params: Record<string, any>
  param_schema: Record<string, any>
  tags: string[]
}

// ==================== 执行记录相关类型 ====================

export interface ChaosExecution {
  id: number
  task_id: number
  host_id: number
  host_info?: Record<string, any>
  start_time?: string
  end_time?: string
  status: string
  chaos_uid?: string
  command?: string
  blade_version?: string
  output?: string
  error_message?: string
  exit_code?: number
  fault_config?: Record<string, any>
  duration_seconds?: number
  retry_count: number
  is_auto_destroyed: boolean
  destroy_time?: string
  destroy_output?: string
  created_at?: string
  updated_at?: string
  created_by?: string
  updated_by?: string
  task_name?: string
  host_name?: string
  is_running: boolean
  is_completed: boolean
  is_successful: boolean
  has_chaos_uid: boolean
  // 关联的任务对象
  task?: ChaosTask
}

export interface ChaosExecutionSearchParams {
  task_id?: number
  task_name?: string
  status?: string
  page?: number
  size?: number
  order_by?: string
  desc?: boolean
}

export interface ChaosExecutionRetryRequest {
  execution_id?: number
  override_params?: Record<string, any>
}

export interface ChaosExecutionBatchRequest {
  execution_ids: number[]
  action: string
}

// ==================== ChaosBlade相关类型 ====================

export interface ChaosBladeInstallRequest {
  env_id: number
  host_ids?: number[]
  force_reinstall?: boolean
}

export interface ChaosBladeStatusResponse {
  host_id: number
  host_name?: string
  host_address?: string
  installed: boolean
  version?: string
  install_path?: string
  last_check_time?: string
  error_message?: string
}

// ==================== 故障类型定义 ====================

export interface FaultType {
  type: string
  label: string
  description: string
  scenarios: FaultScenario[]
}

export interface FaultScenario {
  name: string
  label: string
  description: string
  params: FaultParam[]
}

export interface FaultParam {
  name: string
  label: string
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect'
  required: boolean
  default?: any
  options?: Array<{ label: string; value: any }>
  min?: number
  max?: number
  description?: string
  placeholder?: string
}

// ==================== 表单相关类型 ====================

export interface TaskFormData {
  name: string
  description: string
  env_ids: number[]
  fault_type: string
  fault_params: Record<string, any>
  execution_type: ExecutionType
  scheduled_time: string
  periodic_config: PeriodicConfig
  cron_expression: string
  auto_destroy: boolean
  max_duration: number | undefined
}

export interface ScenarioFormData {
  name: string
  fault_type: string
  description: string
  category: string
  tags: string[]
  default_params: Record<string, any>
  param_schema: Record<string, any>
}

// ==================== 环境和主机相关类型 ====================

export interface Environment {
  id: number
  name: string
  description?: string
  hosts?: Host[]
}

export interface Host {
  id: number
  name: string
  ip: string
  port: number
  status: string
  os_type?: string
  description?: string
}

// ==================== 分页响应类型 ====================

/** 混沌测试任务分页响应 */
export interface ChaosTaskPageResponse {
  items: ChaosTask[]
  total: number
  page: number
  size: number
  pages: number
}

/** 混沌测试执行记录分页响应 */
export interface ChaosExecutionPageResponse {
  items: ChaosExecution[]
  total: number
  page: number
  size: number
  pages: number
}

/** 混沌测试场景分页响应 */
export interface ChaosScenarioPageResponse {
  items: ChaosScenario[]
  total: number
  page: number
  size: number
  pages: number
}
