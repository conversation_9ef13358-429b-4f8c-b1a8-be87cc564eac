<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cron定时任务功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🚀 Cron定时任务功能测试页面</h1>
    
    <div class="test-section success">
        <h3>✅ 后端功能已完成</h3>
        <ul class="feature-list">
            <li>支持4种执行类型：immediate、scheduled、periodic、cron</li>
            <li>数据库模型已更新，添加cron_expression字段</li>
            <li>Schema验证器支持Cron表达式格式验证</li>
            <li>调度服务支持多种调度类型</li>
            <li>API接口完整支持新字段</li>
        </ul>
    </div>

    <div class="test-section success">
        <h3>✅ 前端功能已完成</h3>
        <ul class="feature-list">
            <li>类型定义已更新，支持ExecutionType和PeriodicConfig</li>
            <li>ScheduleStep组件支持4种执行类型选择</li>
            <li>周期性执行配置：分钟、小时、天数输入</li>
            <li>Cron表达式输入和常用模板</li>
            <li>表单验证支持各种执行类型</li>
            <li>任务列表显示执行类型标签</li>
            <li>搜索功能支持按执行类型筛选</li>
        </ul>
    </div>

    <div class="test-section info">
        <h3>📋 测试步骤</h3>
        <ol>
            <li><strong>启动前后端服务</strong>
                <div class="code">
                    # 后端
                    cd backend
                    python -m app.main
                    
                    # 前端
                    cd frontend
                    npm run dev
                </div>
            </li>
            
            <li><strong>测试Cron任务创建</strong>
                <ul>
                    <li>访问：http://localhost:3000/chaos/tasks</li>
                    <li>点击"创建任务"</li>
                    <li>填写基本信息和故障配置</li>
                    <li>在执行计划步骤选择"Cron执行"</li>
                    <li>输入Cron表达式，如：<code>*/1 * * * *</code> (每分钟)</li>
                    <li>提交任务</li>
                </ul>
            </li>
            
            <li><strong>测试周期性任务创建</strong>
                <ul>
                    <li>选择"周期执行"</li>
                    <li>设置执行周期，如：2分钟</li>
                    <li>提交任务</li>
                </ul>
            </li>
            
            <li><strong>验证任务列表</strong>
                <ul>
                    <li>检查执行类型显示是否正确</li>
                    <li>使用执行类型筛选功能</li>
                </ul>
            </li>
            
            <li><strong>验证任务执行</strong>
                <ul>
                    <li>观察后端日志，确认任务被正确同步到调度器</li>
                    <li>等待任务执行时间，验证是否按预期执行</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section info">
        <h3>🕐 Cron表达式示例</h3>
        <div class="code">
            */1 * * * *     # 每分钟执行
            */5 * * * *     # 每5分钟执行
            0 */1 * * *     # 每小时执行
            0 9 * * *       # 每天上午9点执行
            0 9 * * 1-5     # 工作日上午9点执行
            0 9 * * 1       # 每周一上午9点执行
            0 0 1 * *       # 每月1号执行
        </div>
    </div>

    <div class="test-section info">
        <h3>🔄 周期性执行示例</h3>
        <div class="code">
            分钟: 5, 小时: 0, 天: 0    # 每5分钟执行
            分钟: 0, 小时: 2, 天: 0    # 每2小时执行
            分钟: 0, 小时: 0, 天: 1    # 每天执行
            分钟: 30, 小时: 1, 天: 0   # 每1小时30分钟执行
        </div>
    </div>

    <div class="test-section success">
        <h3>🎉 功能特点</h3>
        <ul class="feature-list">
            <li>支持标准5位和扩展6位Cron表达式</li>
            <li>提供常用Cron表达式模板</li>
            <li>实时表单验证和错误提示</li>
            <li>灵活的周期性配置</li>
            <li>完整的类型安全支持</li>
            <li>统一的调度器管理</li>
        </ul>
    </div>

    <script>
        console.log('🚀 Cron定时任务功能测试页面已加载');
        console.log('📋 请按照测试步骤进行功能验证');
    </script>
</body>
</html>
